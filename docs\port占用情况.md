# PORT 占用情况记录

展示出所有服务占用的端口信息，方便后续对新产品所用端口进行分配，同时检查是否有重复占用的情况。

## 基础设施服务

| 服务名称 | 容器名称 | 内部端口 | 外部端口 | 服务类型 | 备注 |
| -------- | -------- | -------- | -------- | -------- | ---- |
| nginx | nginx_service | 80 | 80 | HTTP | Web服务器 |
| nginx | nginx_service | 443 | 443 | HTTPS | Web服务器 |
| *~~mysql~~* | *~~mysql_service~~* | *~~3306~~* | *~~15306~~* | *~~MySQL~~* | *~~数据库服务~~* |
| postgres | postgres_service | 5432 | 15432 | PostgreSQL | 数据库服务 |
| redis | redis_service | 6379 | 16379 | Redis | 缓存服务 |
| certimate | certimate_server | 8910 | 8910 | HTTP | 证书管理服务 |

## 应用服务

| 服务名称 | 容器名称 | 内部端口 | 外部端口 | 服务类型 | 备注 |
| -------- | -------- | -------- | -------- | -------- | ---- |
| study-api | study-api | 8322 | 8322 | HTTP | 学习API服务  |
|           |           | 8324 | 8324 | WebSocket | 学习API WebSocket |
| words-api | words-api | 8312 | 8312 | HTTP | 单词API服务 |
|           |           | 8314 | 8314 | WebSocket | 单词API WebSocket |

