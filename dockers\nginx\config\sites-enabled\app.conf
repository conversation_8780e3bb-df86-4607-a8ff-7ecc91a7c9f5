# 黑羽公司产品能力简介站点配置
# https://app.heivy.cn - 所有产品的二级根地址，展示公司产品能力简介（不宣传具体产品）

# HTTP 服务器 (端口 80) - 重定向到HTTPS
server {
    listen 80;
    server_name app.heivy.cn;

    # 重定向所有HTTP请求到HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS 服务器 (端口 443)
server {
    listen 443 ssl;
    server_name app.heivy.cn;

    # 引入SSL配置
    include /etc/nginx/conf.d/ssl.conf;

    root /usr/share/nginx/www/app;
    index index.html index.htm;

    # 包含所有app下产品的location配置
    include /etc/nginx/sites-enabled/app-sites/*.conf;

    # 静态文件服务 - 黑羽公司产品能力简介页面（优先级最低，放在最后）
    location / {
        try_files $uri $uri/ =404;

        # 添加安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 静态资源缓存优化
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff" always;
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
