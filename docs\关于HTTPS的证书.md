# 关于HTTPS的证书

## 1. 自签名证书

### 1.1 特点

#### 优点

- 通用性 - 自签名证书在任何系统上都能工作，不需要额外安装工具
- 学习价值 - 展示完整的SSL证书生成过程
- 生产兼容 - 自签名证书的流程与生产环境证书流程相似
- 无依赖 - 只需要系统自带的openssl

#### 缺点

- ❌ 浏览器会显示"不安全"警告
- ❌ 需要用户手动接受证书
- ❌ 开发体验不够好

### 1.2 制作方法


### 1.3 测试机安装证书


## 2. mkcert证书

### 2.1 特点

#### 优点

- 完全可信 - 浏览器不会显示任何警告
- 开发友好 - 专门为本地开发设计
- 支持IP地址 - 完美支持 localhost、127.0.0.1、*************
- 一键安装 - 自动安装到系统信任存储
- 跨平台 - Windows、Mac、Linux 都支持

#### 缺点

- ✅ 您的 Mac 电脑 - 完全可信
- ❌ iOS/Android 手机 - 仍然显示不安全警告

#### 原因分析
- mkcert 在您的 Mac 上安装了本地 CA（证书颁发机构）
- 手机没有这个 CA，所以不信任由它签发的证书
- 手机浏览器仍会显示"连接不安全"

## 3. 内网域名 + Let's Encrypt

申请一个域名（如 yourapp.com）
配置 DNS，将子域名指向您的内网IP
申请 Let's Encrypt 证书
手机和电脑都能无警告访问

## 4. 我的建议

对于纯本地开发：
- 继续使用自签名证书
- 手机访问时点击"继续访问"
- 简单直接，无需复杂配置
对于需要移动端测试：
- 申请真实域名 + Let's Encrypt
- 一次配置，所有设备都信任
- 更接近生产环境
对于学习目的：
- 可以尝试 mkcert，了解本地CA的工作原理
- 然后手动安装CA到手机（学习证书信任链）






