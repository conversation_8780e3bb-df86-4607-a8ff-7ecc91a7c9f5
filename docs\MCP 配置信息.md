# MCP 配置信息

## PostgreSQL MCP 配置

```json
{
  "mcpServers": {
    "postgres": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-postgres",
        "postgres://postgres:123456@localhost:5432/hv-app-study"
      ]
    }
  }
}
```

## Redis MCP 配置

### 正确配置
```json
{
  "mcpServers": {
    "redis": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-redis",
        "redis://:123456@localhost:6379"
      ]
    }
  }
}
```
