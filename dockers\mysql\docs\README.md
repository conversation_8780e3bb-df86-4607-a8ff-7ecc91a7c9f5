# MySQL Docker 服务配置

## 概述

这是一个生产级的 MySQL 8.4.5 Docker 配置，包含以下特性：

- ✅ 安全的密钥管理
- ✅ 容器健康检查
- ✅ 资源限制配置
- ✅ 自动备份策略
- ✅ 多数据库支持
- ✅ 性能优化配置

## 目录结构

```
dockers/mysql/
├── env                    # 环境变量配置
├── docker-compose.yml      # Docker Compose 配置
├── config/
│   └── my.cnf             # MySQL 配置文件
├── init/
│   └── init.sql           # 初始化 SQL 脚本
├── backup/
│   └── scripts/           # 备份和管理脚本
│       ├── backup.sh      # 自动备份脚本
│       ├── restore.sh     # 数据恢复脚本
│       └── manage_db.sh   # 数据库管理脚本
└── README.md              # 使用说明
```

## 快速开始

### 0. 准备工作

```bash
# 先在 /home/<USER>/ 创建一个 mysql_data 目录
mkdir -p /home/<USER>/mysql/data
mkdir -p /home/<USER>/mysql/backups

# 999是MySQL容器内mysql用户的UID/GID
sudo chown -R 999:999 /home/<USER>/mysql 

## 999通常是MySQL官方Docker镜像中mysql用户的默认UID/GID，但这不是一个固定的值，可能会根据MySQL镜像版本有所变化。
## 产看方法:
docker run --rm mysql:8.4.5 bash -c "id mysql"
```

### 1. 启动服务

```bash
cd dockers/mysql
docker-compose up -d
```

### 2. 检查服务状态

```bash
docker-compose ps
docker-compose logs mysql
```

### 3. 连接数据库

```bash
# 使用 Docker 容器连接（容器内部）
docker-compose exec mysql mysql -u root -p

# 使用外部客户端连接（从宿主机）
mysql -h localhost -P 15306 -u root -p

# 或使用 127.0.0.1
mysql -h 127.0.0.1 -P 15306 -u root -p
```

### 4. 使用图形化客户端连接

#### Navicat Premium / MySQL Workbench / phpMyAdmin 等

**连接配置**：
- **主机**: `localhost` 或 `127.0.0.1`
- **端口**: `15306`
- **用户名**: `root`
- **密码**: `&o&Iyn*vO9(_`


## 数据库管理

### 创建数据库

#### 1. 创建仅 root 可访问的数据库

```bash
# 使用管理脚本
docker-compose exec mysql /backup/scripts/manage_db.sh create myapp_db

# 或直接使用 SQL
docker-compose exec mysql mysql -u root -p -e "CREATE DATABASE myapp_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

#### 2. 创建数据库并创建专用用户（推荐）

```bash
# 创建数据库和专用用户，只有该用户和 root 可以访问
docker-compose exec mysql /backup/scripts/manage_db.sh create-with-user myapp_db myapp_user 'MyStr0ngP@ssw0rd'

# 示例：为不同项目创建独立的数据库和用户
docker-compose exec mysql /backup/scripts/manage_db.sh create-with-user blog_db blog_user 'Blog#2025!Secure'
docker-compose exec mysql /backup/scripts/manage_db.sh create-with-user shop_db shop_user 'Shop$ecure2025'
```

#### 3. 为现有用户授权访问数据库

```bash
# 为已存在的用户授权访问指定数据库
docker-compose exec mysql /backup/scripts/manage_db.sh grant myapp_db existing_user

# 撤销用户对数据库的访问权限
docker-compose exec mysql /backup/scripts/manage_db.sh revoke myapp_db existing_user
```

### 用户管理

#### 列出所有用户

```bash
docker-compose exec mysql /backup/scripts/manage_db.sh list-users
```

#### 创建新用户（无数据库权限）

```bash
# 创建用户，稍后可以为其授权访问特定数据库
docker-compose exec mysql /backup/scripts/manage_db.sh create-user new_user 'UserP@ssw0rd2025'
```

#### 删除用户

```bash
# 删除用户及其所有权限
docker-compose exec mysql /backup/scripts/manage_db.sh drop-user old_user
```

#### 查看用户权限

```bash
# 查看用户拥有的所有权限
docker-compose exec mysql /backup/scripts/manage_db.sh show-grants myapp_user
```

### 数据库信息查看

#### 列出所有数据库

```bash
docker-compose exec mysql /backup/scripts/manage_db.sh list
```

#### 查看数据库详细信息

```bash
# 查看数据库基本信息
docker-compose exec mysql /backup/scripts/manage_db.sh show myapp_db

# 查看数据库中的表
docker-compose exec mysql /backup/scripts/manage_db.sh tables myapp_db

# 查看数据库大小
docker-compose exec mysql /backup/scripts/manage_db.sh size myapp_db
```

### 删除数据库

```bash
# 删除数据库（需要确认）
docker-compose exec mysql /scripts/manage_db.sh drop myapp_db
```

### 使用场景示例

#### 场景1：为新项目创建完整的数据库环境

```bash
# 1. 创建项目数据库和专用用户
docker-compose exec mysql /scripts/manage_db.sh create-with-user ecommerce_db ecommerce_user 'Ec0mm3rc3#2025'

# 2. 验证创建结果
docker-compose exec mysql /scripts/manage_db.sh show-grants ecommerce_user
docker-compose exec mysql /scripts/manage_db.sh show ecommerce_db

# 3. 应用程序连接配置
# 主机: localhost:15306 (或 127.0.0.1:15306)
# 数据库: ecommerce_db
# 用户名: ecommerce_user
# 密码: Ec0mm3rc3#2025
```

#### 场景2：为现有用户添加新数据库访问权限

```bash
# 1. 创建新数据库
docker-compose exec mysql /scripts/manage_db.sh create analytics_db

# 2. 为现有用户授权
docker-compose exec mysql /scripts/manage_db.sh grant analytics_db ecommerce_user

# 3. 验证权限
docker-compose exec mysql /scripts/manage_db.sh show-grants ecommerce_user
```

#### 场景3：管理多个环境的数据库

```bash
# 开发环境
docker-compose exec mysql /scripts/manage_db.sh create-with-user myapp_dev dev_user 'Dev#Pass2025'

# 测试环境
docker-compose exec mysql /scripts/manage_db.sh create-with-user myapp_test test_user 'Test#Pass2025'

# 预发布环境
docker-compose exec mysql /scripts/manage_db.sh create-with-user myapp_staging staging_user 'Staging#Pass2025'
```

## 备份和恢复

### 手动备份

```bash
# 备份所有数据库
docker-compose exec mysql-backup /scripts/backup.sh

# 查看备份文件
docker-compose exec mysql /scripts/manage_db.sh backup-list
```

### 恢复数据库

```bash
# 恢复指定数据库
docker-compose exec mysql /scripts/restore.sh myapp_db myapp_db_20250106_143000.sql.gz

# 注意：恢复数据库后，需要重新设置用户权限
# 1. 恢复数据库
docker-compose exec mysql /scripts/restore.sh ecommerce_db ecommerce_db_20250106_143000.sql.gz

# 2. 重新创建用户并授权（如果用户不存在）
docker-compose exec mysql /scripts/manage_db.sh create-user ecommerce_user 'Ec0mm3rc3#2025'
docker-compose exec mysql /scripts/manage_db.sh grant ecommerce_db ecommerce_user

# 或者，如果用户已存在，只需重新授权
docker-compose exec mysql /scripts/manage_db.sh grant ecommerce_db ecommerce_user
```

### 自动备份

备份服务会根据 `env` 文件中的 `BACKUP_SCHEDULE` 配置自动执行备份：

- 默认每天凌晨 2 点执行备份
- 自动清理超过 7 天的备份文件
- 备份文件存储在 `./backup/` 目录

## 配置说明

### 环境变量 (env)

```bash
# 数据库密码
MYSQL_ROOT_PASSWORD=&o&Iyn*vO9(_

# 字符集配置
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_unicode_ci

# 端口配置
MYSQL_HOST_PORT=15306        # 宿主机端口
MYSQL_CONTAINER_PORT=3306    # 容器内端口

# 资源限制
MYSQL_MEMORY_LIMIT=2g
MYSQL_CPU_LIMIT=1.0
MYSQL_CPU_RESERVATION=0.5

# 备份配置
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE=0 2 * * *
```

### 性能配置

MySQL 配置已针对 2GB 内存限制进行优化：

- InnoDB 缓冲池: 1GB
- 最大连接数: 200
- 查询缓存: 64MB
- 启用慢查询日志

### 安全配置

- 使用环境变量管理密码
- 禁用本地文件加载
- 运行在非 root 用户下
- 启用安全选项
- **端口安全**: 只允许宿主机连接（127.0.0.1:15306），外部网络无法直接访问

## 监控和日志

### 查看日志

```bash
# MySQL 服务日志
docker-compose logs mysql

# 备份服务日志
docker-compose logs mysql-backup

# 错误日志
docker-compose exec mysql tail -f /var/log/mysql/error.log

# 慢查询日志
docker-compose exec mysql tail -f /var/log/mysql/slow.log
```

### 健康检查

```bash
# 检查容器健康状态
docker-compose ps
docker inspect mysql_service | grep Health -A 10
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker-compose logs mysql

   # 检查配置文件语法
   docker-compose config
   ```

2. **连接被拒绝**
   ```bash
   # 检查端口是否开放（注意是15306端口）
   netstat -tlnp | grep 15306

   # 检查是否使用正确的端口连接
   mysql -h localhost -P 15306 -u root -p

   # 确认只能从本机连接（127.0.0.1）
   mysql -h 127.0.0.1 -P 15306 -u root -p

   # 外部IP无法连接（这是正常的安全设置）
   # mysql -h ************* -P 15306 -u root -p  # 这会失败
   ```

3. **用户无法访问数据库**
   ```bash
   # 检查用户是否存在
   docker-compose exec mysql /backup/scripts/manage_db.sh list-users

   # 检查用户权限
   docker-compose exec mysql /backup/scripts/manage_db.sh show-grants username

   # 重新授权
   docker-compose exec mysql /backup/scripts/manage_db.sh grant database_name username
   ```

4. **忘记用户密码**
   ```bash
   # 重置用户密码
   docker-compose exec mysql mysql -u root -p -e "ALTER USER 'username'@'%' IDENTIFIED BY 'new_password';"

   # 或者删除用户重新创建
   docker-compose exec mysql /backup/scripts/manage_db.sh drop-user username
   docker-compose exec mysql /backup/scripts/manage_db.sh create-user username 'new_password'
   docker-compose exec mysql /backup/scripts/manage_db.sh grant database_name username
   ```

5. **应用程序连接失败**
   ```bash
   # 检查用户权限
   docker-compose exec mysql /scripts/manage_db.sh show-grants app_user

   # 检查数据库是否存在
   docker-compose exec mysql /scripts/manage_db.sh list

   # 测试连接
   docker-compose exec mysql mysql -u app_user -p -h mysql database_name -e "SELECT 1;"
   ```

6. **备份失败**
   ```bash
   # 检查备份服务日志
   docker-compose logs mysql-backup

   # 手动执行备份测试
   docker-compose exec mysql-backup /scripts/backup.sh
   ```

### 重置数据

```bash
# 停止服务
docker-compose down

# 删除数据卷（注意：这会删除所有数据）
docker volume rm mysql_data mysql_logs mysql_backups

# 重新启动
docker-compose up -d
```

## 升级和维护

### 升级 MySQL 版本

1. 备份所有数据
2. 修改 `docker-compose.yml` 中的镜像版本
3. 重新启动服务

### 定期维护

- 定期检查备份文件
- 监控磁盘空间使用
- 检查慢查询日志
- 更新密码策略

## 注意事项

1. **生产环境使用**：
   - 修改默认密码
   - 配置 SSL/TLS
   - 设置防火墙规则
   - 配置监控告警

2. **数据安全**：
   - 定期测试备份恢复
   - 异地备份存储
   - 访问权限控制

3. **用户权限管理最佳实践**：
   - **最小权限原则**：每个用户只授予必要的数据库访问权限
   - **独立用户**：为每个应用程序创建独立的数据库用户
   - **强密码策略**：使用复杂密码，包含大小写字母、数字和特殊字符
   - **定期审查**：定期检查用户权限，删除不再需要的用户
   - **环境隔离**：开发、测试、生产环境使用不同的用户和密码

4. **密码管理建议**：
   ```bash
   # 好的密码示例
   MyApp#2025!Secure    # 包含大小写、数字、特殊字符
   Pr0ject$DB@2025     # 易记但复杂

   # 避免的密码
   123456              # 太简单
   password            # 常见词汇
   myapp               # 与应用名相同
   ```

5. **权限分配示例**：
   ```bash
   # 应用程序用户：只能访问应用数据库
   docker-compose exec mysql /backup/scripts/manage_db.sh create-with-user app_db app_user 'App#2025!Secure'

   # 只读用户：用于报表和分析
   docker-compose exec mysql /backup/scripts/manage_db.sh create-user readonly_user 'Read#2025!Only'
   docker-compose exec mysql mysql -u root -p -e "GRANT SELECT ON app_db.* TO 'readonly_user'@'%';"

   # 备份用户：只能执行备份操作
   docker-compose exec mysql /backup/scripts/manage_db.sh create-user backup_user 'Backup#2025!User'
   docker-compose exec mysql mysql -u root -p -e "GRANT SELECT, LOCK TABLES ON *.* TO 'backup_user'@'%';"
   ```

6. **性能优化**：
   - 根据实际负载调整配置
   - 监控资源使用情况
   - 定期分析慢查询

7. **安全检查清单**：
   - [ ] 所有用户都使用强密码
   - [ ] 每个应用程序都有独立的数据库用户
   - [ ] 没有不必要的用户权限
   - [ ] 定期更新密码
   - [ ] 监控异常登录活动
