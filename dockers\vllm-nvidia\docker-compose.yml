# vLLM 通用基础配置
# 此文件包含所有模型的通用配置，由模型专用配置文件覆盖特定参数

services:
  vllm-server:
    image: vllm/vllm-openai:v0.10.0
    container_name: vllm_server
    ports:
      - "18080:18080"
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - PYTHONPATH=/workspace
      - TRUST_REMOTE_CODE=true
      - HF_HOME=/models/.caches/huggingface-cache
      - TRANSFORMERS_CACHE=/models/.caches/huggingface-cache
      - HF_HUB_CACHE=/models/.caches/huggingface-cache
      - VLLM_API_KEY=your-api-key-here
      - TOKENIZERS_PARALLELISM=false
      # 以下参数将被模型专用配置覆盖
      - MODEL_NAME=default
      - MODEL_PATH=/models/default
      - MAX_MODEL_LEN=2048
      - TENSOR_PARALLEL_SIZE=1
      - GPU_MEMORY_UTILIZATION=0.9
    volumes:
      # 模型存储挂载
      - D:/.llm-models:/models
    working_dir: /workspace
    networks:
      - vllm_network
    restart: unless-stopped
    # GPU支持 - WSL2 模式使用传统 runtime 方式
    runtime: nvidia
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    # 默认启动命令（将被模型专用配置覆盖）- 使用容器默认入口点
    command: [
      "--model", "/models/default",
      "--host", "0.0.0.0",
      "--port", "18080",
      "--trust-remote-code",
      "--max-model-len", "2048",
      "--tensor-parallel-size", "1",
      "--gpu-memory-utilization", "0.9",
      "--api-key", "your-api-key-here"
    ]

networks:
  vllm_network:
    driver: bridge
