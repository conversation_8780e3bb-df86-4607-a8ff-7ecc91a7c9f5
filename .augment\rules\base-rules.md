---
type: "manual"---
# 基准操作类
优先级：最高，不可被其他规则覆盖
- 应答规则：Always respond in 中文;
- 声明规则：每次回答, 先说使用的是哪个LLM模型, 格式如：【模型：名称和版本】
- 读取规则：读取数据、文件，获取信息等, 不用确认, 直接读取;
- 中英文规则：代码注释以中文为主；代码正文必须全是英文，不要使用emoji;
- root密码: 当需入输入当前系统的管理员权限的密码时, 密码是: 一个空格

# 计划任务类
优先级：高，不可被其他规则覆盖
- task-clear规则: 当我说 task clear 时，你要停止并清理掉当前的所有augment task;
- task规则: 当我说 task 时，然后你要仔细分析要求, 制定详细的任务计划, 创建 augment task, 然后按照计划执行 augment task;

# MCP使用类
优先级：高，不可被其他规则覆盖
- postgres: 优先用MCP工具里的postgres来处理数据库;
- redis: 优先用MCP工具里的redis来处理redis数据库;

# git 操作类
优先级：高，不可被其他规则覆盖
- msg规则:当我说 git msg 时，你要根据 ./git-commit-msg.md 的要求生成msg;
- commit规则:  当我说 git commit 时，你要根据 ./git-commit-msg.md 的要求生成msg ，并commit;
- push规则: 当我说 git push 时，你要完成上一条规则(commit规则)，同时要完成 git push;

# 修改操作类
优先级：中，可以被后续规则覆盖
- 更改规则：当需要修改文件、创建目录、创建文件等产生文件系统变更时, 给出完整修改方案, 经确认后, 才可以修改;
- 分步骤规则:  第一步：分析问题, 第二步：提出方案, 第三步：等待确认,第四步：执行修改.