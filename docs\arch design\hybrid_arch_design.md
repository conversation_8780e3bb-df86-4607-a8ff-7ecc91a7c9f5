# 混合轻量架构产品验证平台设计

## 1. 概述

### 1.1 设计理念

采用混合轻量架构，在成本控制和技术灵活性之间找到最佳平衡点。通过单体多租户设计和资源共享策略，实现低成本快速验证，同时保持向独立架构平滑迁移的能力。

### 1.2 适用范围

本文档描述基于容器化单体应用的产品验证平台后端架构设计，适用于需要在成本、复杂度和扩展性之间平衡的产品验证场景。

### 1.3 核心优势

- **成本可控**: 月成本控制在¥200-400，比传统微服务降低60%
- **管理简单**: 单容器部署，运维复杂度低
- **快速迭代**: 新产品快速接入，无需独立部署
- **平滑扩展**: 成功产品可无缝迁移到独立架构
- **技术统一**: 统一技术栈，降低学习成本

## 2. 产品类型

- **Web型**: 研发垂直领域的 SaaS型、工具性、AI助手型等产品
- **手机APP**: 研发好用的，能解决用户某一特定问题的移动应用APP
- **小游戏**: H5小程序、小游戏类的产品
- **手机游戏**: 使用unity制作的手机游戏

## 3. 混合架构系统设计

### 3.1 计算服务架构

#### 3.1.1 统一API服务 (unified-api)
- **单体多租户**: 一个Go服务处理所有产品请求
- **产品隔离**: 通过路由参数和中间件实现产品级别隔离
- **插件化设计**: 每个产品作为独立的Go package
- **热部署**: 支持产品模块的动态加载和卸载

#### 3.1.2 服务架构设计
```
unified-api服务结构:
├── main.go                 # 主服务入口
├── router/                 # 统一路由管理
│   ├── router.go          # 主路由配置
│   └── middleware/        # 中间件
├── products/              # 产品模块
│   ├── greet/            # greet产品
│   │   ├── handler/      # 业务处理
│   │   ├── model/        # 数据模型
│   │   └── service/      # 业务逻辑
│   └── words/            # words产品
│       ├── handler/
│       ├── model/
│       └── service/
├── shared/               # 共享组件
│   ├── database/         # 数据库连接
│   ├── redis/           # 缓存服务
│   ├── utils/           # 工具函数
│   └── config/          # 配置管理
└── deploy/              # 部署配置
    ├── Dockerfile
    ├── docker-compose.yml
    └── nginx.conf
```

### 3.2 数据存储架构

#### 3.2.1 PostgreSQL多Schema隔离
- **单实例部署**: 一个PostgreSQL实例服务所有产品
- **Schema隔离**: 每个产品使用独立的schema
- **命名规范**: 
  - Schema命名: `{product_name}_schema`
  - 示例: `greet_schema`, `words_schema`
- **权限控制**: 每个产品使用独立的数据库用户

#### 3.2.2 Redis命名空间隔离
- **单实例部署**: 一个Redis实例服务所有产品
- **Key前缀隔离**: `{product}:{module}:{key}`
- **示例**: 
  - `greet:user:12345`
  - `words:dict:cache:en`
- **TTL策略**: 根据产品特性设置不同的过期时间

### 3.3 文件存储服务

#### 3.3.1 腾讯云COS分层存储
- **存储桶组织**:
  - `validation-platform-static`: 所有产品的静态资源
  - `validation-platform-uploads`: 用户上传文件
- **目录结构**:
```
/static/
  /{product}/
    /css/
    /js/
    /images/
/uploads/
  /{product}/
    /{user_id}/
/temp/
  /{product}/
```

#### 3.3.2 CDN配置
- **腾讯云CDN**: 加速静态资源访问
- **缓存策略**: 
  - 静态资源: 7天缓存
  - 动态内容: 不缓存
- **域名配置**: `static.yourdomain.com`

### 3.4 反向代理与负载均衡

#### 3.4.1 Nginx配置
- **统一入口**: 所有请求通过Nginx路由
- **路由规则**:
```nginx
# API路由
location /api/ {
    proxy_pass http://unified-api:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# 静态资源路由
location /static/ {
    proxy_pass https://static.yourdomain.com;
}

# 健康检查
location /health {
    proxy_pass http://unified-api:8080/health;
}
```

#### 3.4.2 负载均衡策略
- **单实例部署**: 验证期间使用单实例
- **水平扩展**: 成功产品可扩展为多实例
- **健康检查**: 自动检测服务健康状态

## 4. 开发架构

### 4.1 技术栈选择

#### 4.1.1 后端技术栈
```go
核心框架:
- Gin: HTTP路由和中间件
- GORM: ORM数据库操作
- go-redis: Redis客户端
- Viper: 配置管理
- Logrus: 结构化日志
- Swagger: API文档生成
```

#### 4.1.2 产品模块接口
```go
// 产品接口定义
type ProductModule interface {
    // 注册路由
    RegisterRoutes(router *gin.RouterGroup)
    
    // 初始化产品
    Initialize(config *Config) error
    
    // 健康检查
    HealthCheck() error
    
    // 清理资源
    Cleanup() error
}

// 产品配置
type ProductConfig struct {
    Name        string
    Version     string
    Enabled     bool
    Database    DatabaseConfig
    Redis       RedisConfig
    Features    map[string]interface{}
}
```

### 4.2 配置管理

#### 4.2.1 环境配置
```yaml
# config.yml
server:
  port: 8080
  mode: release

database:
  host: localhost
  port: 5432
  user: postgres
  password: ${DB_PASSWORD}
  
redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}

products:
  greet:
    enabled: true
    version: "v1"
    features:
      user_management: true
      analytics: false
  words:
    enabled: true
    version: "v1"
    features:
      dictionary: true
      translation: false
```

#### 4.2.2 动态配置
- **配置热更新**: 支持运行时修改产品配置
- **特性开关**: 通过配置控制产品功能启停
- **A/B测试**: 支持产品功能的灰度发布

### 4.3 API设计规范

#### 4.3.1 路由设计
```
统一路由模式: /api/{product}/{version}/{resource}

示例:
- GET /api/greet/v1/users
- POST /api/words/v1/dictionary
- PUT /api/greet/v1/users/{id}

管理接口:
- GET /api/admin/products          # 产品列表
- POST /api/admin/products/{name}  # 启用产品
- DELETE /api/admin/products/{name} # 禁用产品
```

#### 4.3.2 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "product": "greet",
    "version": "v1",
    "result": {...}
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

## 5. 部署架构

### 5.1 容器化部署

#### 5.1.1 Docker配置
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o unified-api main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/unified-api .
COPY --from=builder /app/config.yml .
CMD ["./unified-api"]
```

#### 5.1.2 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - unified-api

  unified-api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: validation_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgres/data

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 5.2 云服务器部署

#### 5.2.1 腾讯云轻量应用服务器
- **配置推荐**: 2核4GB，40GB SSD
- **操作系统**: Ubuntu 22.04 LTS
- **月成本**: ≈ ¥120-180

#### 5.2.2 部署脚本
```bash
#!/bin/bash
# deploy.sh

# 更新代码
git pull origin main

# 构建镜像
docker-compose build

# 停止旧服务
docker-compose down

# 启动新服务
docker-compose up -d

# 健康检查
sleep 10
curl -f http://localhost/health || exit 1

echo "部署完成"
```

## 6. 监控与运维

### 6.1 监控体系

#### 6.1.1 应用监控
- **健康检查**: `/health` 端点监控服务状态
- **性能指标**: 响应时间、吞吐量、错误率
- **业务指标**: 各产品的用户活跃度、功能使用情况

#### 6.1.2 基础设施监控
- **服务器监控**: CPU、内存、磁盘、网络使用率
- **数据库监控**: 连接数、查询性能、存储空间
- **缓存监控**: Redis内存使用、命中率、连接数

### 6.2 日志管理

#### 6.2.1 结构化日志
```go
// 日志格式
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "info",
  "product": "greet",
  "user_id": "12345",
  "request_id": "req_123456789",
  "message": "user login success",
  "duration": "150ms"
}
```

#### 6.2.2 日志收集
- **本地日志**: 容器内日志文件
- **集中收集**: 可选接入ELK或腾讯云日志服务
- **日志轮转**: 自动清理过期日志文件

### 6.3 告警配置

#### 6.3.1 关键指标告警
- API响应时间 > 2秒
- 错误率 > 3%
- 服务器CPU > 80%
- 数据库连接数 > 80%
- 磁盘使用率 > 85%

## 7. 成本分析

### 7.1 成本估算

#### 7.1.1 月度成本预估
```
腾讯云轻量应用服务器:
- 2核4GB配置: ¥150/月

腾讯云COS存储:
- 存储: 50GB ≈ ¥10/月
- 流量: ≈ ¥20/月

腾讯云CDN:
- 流量: ≈ ¥30/月

域名和SSL证书:
- ≈ ¥10/月

总计: ≈ ¥220/月 (支持多个产品)
平均每产品: ≈ ¥55/月 (按4个产品计算)
```

### 7.2 成本优化策略

- **资源共享**: 多产品共享基础设施
- **按需扩容**: 根据实际使用情况调整配置
- **缓存优化**: 减少数据库查询和外部API调用
- **CDN使用**: 降低带宽成本

## 8. 产品生命周期管理

### 8.1 产品接入流程

1. **代码开发**: 基于产品模块接口开发新产品
2. **配置添加**: 在配置文件中添加产品配置
3. **数据库初始化**: 创建产品专用schema和表结构
4. **功能测试**: 在测试环境验证产品功能
5. **生产部署**: 通过配置开关启用新产品

### 8.2 产品管理操作

#### 8.2.1 产品启停控制
```bash
# 启用产品
curl -X POST /api/admin/products/newproduct \
  -H "Content-Type: application/json" \
  -d '{"enabled": true, "version": "v1"}'

# 禁用产品
curl -X DELETE /api/admin/products/failedproduct
```

#### 8.2.2 产品配置更新
```bash
# 更新产品配置
curl -X PUT /api/admin/products/greet/config \
  -H "Content-Type: application/json" \
  -d '{"features": {"analytics": true}}'
```

### 8.3 产品下架流程

1. **停止服务**: 通过配置禁用产品API
2. **数据备份**: 导出产品schema数据
3. **清理缓存**: 删除产品相关的Redis数据
4. **代码清理**: 移除产品模块代码
5. **配置清理**: 删除产品配置项

### 8.4 产品迁移流程

#### 8.4.1 迁移到独立服务
```bash
# 1. 导出产品数据
pg_dump -n greet_schema validation_platform > greet_data.sql

# 2. 提取产品代码
cp -r products/greet/ ../greet-standalone/

# 3. 创建独立部署配置
# 4. 部署独立服务
# 5. 数据迁移验证
# 6. 切换流量
# 7. 清理原产品模块
```

#### 8.4.2 迁移到Serverless
- 将产品模块改造为云函数
- 数据库迁移到Serverless数据库
- API网关配置更新
- 流量逐步切换

## 9. 安全策略

### 9.1 访问控制

#### 9.1.1 API安全
- **JWT认证**: 统一的用户认证机制
- **产品隔离**: 用户只能访问授权的产品
- **API限流**: 防止恶意请求和DDoS攻击
- **CORS配置**: 限制跨域访问来源

#### 9.1.2 管理接口安全
- **管理员认证**: 独立的管理员认证体系
- **操作审计**: 记录所有管理操作日志
- **IP白名单**: 限制管理接口访问来源

### 9.2 数据安全

#### 9.2.1 数据隔离
- **Schema隔离**: 产品间数据完全隔离
- **用户权限**: 数据库用户权限最小化
- **敏感数据**: 密码、密钥等敏感信息加密存储

#### 9.2.2 传输安全
- **HTTPS强制**: 所有API强制使用HTTPS
- **证书管理**: 自动更新SSL证书
- **数据加密**: 敏感数据传输加密

## 10. 技术选型说明

### 10.1 为什么选择混合架构

1. **成本平衡**: 在成本和功能之间找到最佳平衡点
2. **管理简化**: 相比微服务，运维复杂度大幅降低
3. **技术统一**: 统一技术栈，降低学习和维护成本
4. **扩展灵活**: 支持向微服务或Serverless平滑迁移

### 10.2 架构优势

#### 10.2.1 相比微服务架构
- **成本更低**: 资源共享，减少基础设施开销
- **部署简单**: 单容器部署，无需复杂的服务编排
- **调试容易**: 单进程调试，问题定位更直接

#### 10.2.2 相比单体架构
- **模块隔离**: 产品间代码和数据隔离
- **独立扩展**: 成功产品可独立扩展
- **技术演进**: 支持向现代架构平滑迁移

### 10.3 适用场景

- **产品验证阶段**: 快速验证多个产品想法
- **中小团队**: 技术团队规模有限，需要简化运维
- **成本敏感**: 对基础设施成本有严格控制要求
- **快速迭代**: 需要频繁发布和更新产品功能

## 11. 最佳实践

### 11.1 开发最佳实践

#### 11.1.1 代码组织
- **模块独立**: 产品模块间避免直接依赖
- **接口统一**: 所有产品模块实现统一接口
- **配置外置**: 业务配置通过配置文件管理
- **错误处理**: 统一的错误处理和日志记录

#### 11.1.2 数据库设计
- **Schema命名**: 使用清晰的命名规范
- **表结构**: 包含统一的审计字段(created_at, updated_at)
- **索引优化**: 根据查询模式优化索引
- **数据迁移**: 使用版本化的数据库迁移脚本

### 11.2 运维最佳实践

#### 11.2.1 部署策略
- **蓝绿部署**: 零停机时间部署
- **健康检查**: 部署后自动验证服务健康
- **回滚机制**: 快速回滚到上一个稳定版本
- **配置管理**: 环境配置与代码分离

#### 11.2.2 监控告警
- **分层监控**: 基础设施、应用、业务三层监控
- **告警分级**: 根据严重程度设置不同告警级别
- **自动恢复**: 对于常见问题实现自动恢复机制

## 12. 总结

混合轻量架构为产品验证平台提供了成本、复杂度和扩展性的最佳平衡方案。通过单体多租户设计和资源共享策略，在保持低成本的同时，提供了足够的灵活性和扩展能力。

### 12.1 核心价值

- **成本效益**: 相比传统微服务架构节省60%以上成本
- **快速验证**: 新产品可在数小时内上线验证
- **平滑演进**: 支持向更复杂架构的无缝迁移
- **运维友好**: 大幅降低运维复杂度和学习成本

### 12.2 适用场景

该架构特别适合：
- 初创公司的多产品验证
- 中小团队的快速迭代
- 成本敏感的产品试验
- 需要技术栈统一的团队

### 12.3 发展路径

随着产品验证的进展，可以选择不同的演进路径：
- **继续优化**: 在当前架构基础上持续优化
- **微服务拆分**: 成功产品拆分为独立微服务
- **Serverless迁移**: 迁移到更低成本的Serverless架构
- **混合部署**: 不同产品采用不同的部署策略
