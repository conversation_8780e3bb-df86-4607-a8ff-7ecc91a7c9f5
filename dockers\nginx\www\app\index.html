<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用宣传页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .port-list {
            list-style-type: none;
            padding: 0;
        }
        .port-list li {
            background: #f9f9f9;
            margin: 5px 0;
            padding: 10px;
            border-radius: 3px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Nginx 配置测试成功！</h1>
        
        <div class="status">
            <strong>✅ 状态：</strong> Nginx 服务正在运行
        </div>
        
        <div class="info">
            <h3>📋 配置信息：</h3>
            <p><strong>Nginx版本：</strong> 1.28.0</p>
            <p><strong>容器名称：</strong> nginx_service</p>
        </div>
        
        <div class="info">
            <h3>🌐 可访问的端口：</h3>
            <ul class="port-list">
                <li><strong>HTTP:</strong> http://localhost (端口 80)</li>
                <li><strong>HTTPS:</strong> https://localhost (端口 443)</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>🔧 支持的功能：</h3>
            <ul>
                <li>✅ HTTPS/SSL 支持（自签名证书）</li>
                <li>✅ WebSocket (WS) 支持</li>
                <li>✅ WebSocket Secure (WSS) 支持</li>
                <li>✅ 多服务架构支持</li>
                <li>✅ 多域名/IP支持</li>
            </ul>
        </div>

        <div class="info">
            <p><strong>⏰ 测试时间：</strong> <span id="datetime"></span></p>
        </div>
    </div>
    
    <script>
        document.getElementById('datetime').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
