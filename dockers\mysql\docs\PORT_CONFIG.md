# MySQL 端口配置说明

## 当前端口配置

```yaml
ports:
  - "127.0.0.1:15306:3306"
```

## 配置解释

### 端口映射格式
`"绑定IP:宿主机端口:容器端口"`

### 具体含义
- **127.0.0.1**: 只允许本机（宿主机）访问，外部网络无法连接
- **15306**: 宿主机端口，外部连接时使用此端口
- **3306**: 容器内 MySQL 服务端口（标准 MySQL 端口）

## 安全特性

### ✅ 安全优势
1. **本机访问限制**: 只有宿主机可以连接数据库
2. **外部隔离**: 外部网络无法直接访问数据库
3. **非标准端口**: 使用 15306 而非标准 3306 端口
4. **容器网络**: 应用容器仍可通过 Docker 网络内部访问

### 🚫 外部访问被阻止
```bash
# 这些连接会失败（安全设计）
mysql -h ************* -P 15306 -u root -p
mysql -h 外部IP -P 15306 -u root -p
```

## 连接方式

### 从宿主机连接
```bash
# 使用 localhost
mysql -h localhost -P 15306 -u root -p

# 使用 127.0.0.1
mysql -h 127.0.0.1 -P 15306 -u root -p

# 连接指定数据库
mysql -h localhost -P 15306 -u myapp_user -p myapp_db
```

### 从容器内连接
```bash
# 容器间通信使用服务名和标准端口
docker-compose exec mysql mysql -u root -p
docker-compose exec app_container mysql -h mysql -P 3306 -u myapp_user -p myapp_db
```

### 应用程序连接字符串
```bash
# JDBC (Java)
*************************************

# Python (pymysql/mysql-connector)
mysql://myapp_user:password@localhost:15306/myapp_db

# Node.js (mysql2)
{
  host: 'localhost',
  port: 15306,
  user: 'myapp_user',
  password: 'password',
  database: 'myapp_db'
}

# PHP (PDO)
$pdo = new PDO('mysql:host=localhost;port=15306;dbname=myapp_db', 'myapp_user', 'password');
```

## 端口配置变更

### 修改端口
如需修改端口，编辑 `env` 文件：
```bash
# 修改宿主机端口
MYSQL_HOST_PORT=25306

# 容器端口通常不需要修改
MYSQL_CONTAINER_PORT=3306
```

### 允许外部访问（不推荐）
如果需要允许外部访问，修改 `docker-compose.yml`：
```yaml
ports:
  - "${MYSQL_HOST_PORT}:${MYSQL_CONTAINER_PORT}"  # 移除 127.0.0.1 限制
```

### 完全禁用外部访问
如果不需要从宿主机访问，可以删除 ports 配置：
```yaml
# 删除 ports 配置
# 只能通过容器网络访问
```

## 网络架构

```
外部网络 ❌ ──────────────────────────────────┐
                                              │
宿主机 ✅ ─── localhost:15306 ─── Docker ─── mysql:3306
                                   │
应用容器 ✅ ─── mysql:3306 ────────┘
```

## 故障排除

### 检查端口监听
```bash
# 检查端口是否正确监听
netstat -tlnp | grep 15306
ss -tlnp | grep 15306

# 应该看到类似输出：
# tcp 0 0 127.0.0.1:15306 0.0.0.0:* LISTEN
```

### 测试连接
```bash
# 测试本机连接
telnet localhost 15306

# 测试 MySQL 连接
mysql -h localhost -P 15306 -u root -p -e "SELECT 1;"
```

### 常见错误
1. **端口错误**: 使用了 3306 而非 15306
2. **IP 错误**: 尝试从外部 IP 连接
3. **防火墙**: 本机防火墙阻止了连接

## 生产环境建议

### 推荐配置（当前使用）
```yaml
ports:
  - "127.0.0.1:15306:3306"
```

### 高安全环境
```yaml
# 不暴露任何端口，只允许容器间通信
# 删除 ports 配置
```

### 开发环境（可选）
```yaml
ports:
  - "127.0.0.1:3306:3306"  # 使用标准端口便于开发
```

## 总结

当前配置 `"127.0.0.1:15306:3306"` 提供了：
- ✅ **安全性**: 只允许宿主机访问
- ✅ **隔离性**: 外部网络无法直接连接
- ✅ **灵活性**: 宿主机可以正常使用数据库
- ✅ **兼容性**: 容器间通信不受影响

这是一个平衡安全性和可用性的理想配置。
