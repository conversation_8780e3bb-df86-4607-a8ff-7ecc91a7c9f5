--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Debian 17.5-1.pgdg120+1)
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: circuit; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA circuit;


ALTER SCHEMA circuit OWNER TO postgres;

--
-- Name: passive; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA passive;


ALTER SCHEMA passive OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: circuit_conf_element; Type: TABLE; Schema: circuit; Owner: postgres
--

CREATE TABLE circuit.circuit_conf_element (
    id integer NOT NULL,
    type character varying(255),
    prefix character varying(255),
    category character varying(255),
    pininfo character varying(255),
    hide integer,
    forbidden integer,
    icon character varying(255),
    "desc" character varying(255),
    "group" numeric(20,0)
);


ALTER TABLE circuit.circuit_conf_element OWNER TO postgres;

--
-- Name: circuit_conf_element_property; Type: TABLE; Schema: circuit; Owner: postgres
--

CREATE TABLE circuit.circuit_conf_element_property (
    id integer NOT NULL,
    element_id integer,
    property_id integer,
    order_no integer,
    category character varying(255),
    value text,
    unit character varying(255),
    expression character varying(255),
    annotate integer,
    hide integer,
    readonly integer,
    minimum character varying(255),
    maximum character varying(255),
    is_simu integer,
    influence text,
    range character varying(255)
);


ALTER TABLE circuit.circuit_conf_element_property OWNER TO postgres;

--
-- Name: circuit_conf_option; Type: TABLE; Schema: circuit; Owner: postgres
--

CREATE TABLE circuit.circuit_conf_option (
    id integer NOT NULL,
    feature character varying(255),
    name character varying(255),
    option text
);


ALTER TABLE circuit.circuit_conf_option OWNER TO postgres;

--
-- Name: circuit_conf_property; Type: TABLE; Schema: circuit; Owner: postgres
--

CREATE TABLE circuit.circuit_conf_property (
    id integer NOT NULL,
    name character varying(255),
    kind character varying(255),
    type character varying(255),
    "values" character varying(255),
    units character varying(255),
    forbidden integer
);


ALTER TABLE circuit.circuit_conf_property OWNER TO postgres;

--
-- Name: circuit_conf_result; Type: TABLE; Schema: circuit; Owner: postgres
--

CREATE TABLE circuit.circuit_conf_result (
    id integer NOT NULL,
    element_id integer,
    dataset character varying(255),
    attribute character varying(255),
    unit character varying(255),
    vector_opt character varying(255),
    scalar_opt character varying(255),
    scale integer,
    legend character varying(255),
    view_data text,
    line_color character varying(255),
    line_style character varying(255),
    line_width integer,
    marker character varying(255),
    make_size integer,
    x_axis_loc character varying(255),
    y_axis_loc character varying(255),
    annotate integer,
    display integer,
    forbidden integer
);


ALTER TABLE circuit.circuit_conf_result OWNER TO postgres;

--
-- Name: conf_object; Type: TABLE; Schema: passive; Owner: postgres
--

CREATE TABLE passive.conf_object (
    id integer NOT NULL,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    deleted_at timestamp(6) without time zone,
    type_name text,
    display_name text,
    prefix text,
    category text,
    icon text,
    description text
);


ALTER TABLE passive.conf_object OWNER TO postgres;

--
-- Name: conf_object_hook; Type: TABLE; Schema: passive; Owner: postgres
--

CREATE TABLE passive.conf_object_hook (
    id integer NOT NULL,
    name text NOT NULL,
    name_zh text NOT NULL,
    type text NOT NULL,
    stages text NOT NULL,
    description text NOT NULL,
    labels text NOT NULL,
    function_body text NOT NULL,
    where_clause text NOT NULL
);


ALTER TABLE passive.conf_object_hook OWNER TO postgres;

--
-- Name: conf_object_hook_conf_object_property; Type: TABLE; Schema: passive; Owner: postgres
--

CREATE TABLE passive.conf_object_hook_conf_object_property (
    conf_object_hook_id integer NOT NULL,
    conf_object_property_id integer NOT NULL
);


ALTER TABLE passive.conf_object_hook_conf_object_property OWNER TO postgres;

--
-- Name: conf_object_property; Type: TABLE; Schema: passive; Owner: postgres
--

CREATE TABLE passive.conf_object_property (
    id integer NOT NULL,
    created_at timestamp(6) without time zone,
    updated_at timestamp(6) without time zone,
    deleted_at timestamp(6) without time zone,
    object_id integer,
    order_no integer,
    property_name text,
    display_name text,
    path character varying(250),
    type text,
    value text,
    unit text,
    "values" text,
    units text,
    expression text,
    hidden integer,
    readonly integer,
    range text,
    description text,
    checker text
);


ALTER TABLE passive.conf_object_property OWNER TO postgres;

--
-- Name: laser_conf_compose; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_compose (
    id integer NOT NULL,
    object_type bigint NOT NULL,
    object_id bigint NOT NULL,
    key character varying(128) NOT NULL,
    value text,
    value_type character varying(16) NOT NULL,
    comment character varying(2048),
    "timestamp" timestamp without time zone NOT NULL,
    updater character varying(255) NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_compose OWNER TO postgres;

--
-- Name: COLUMN laser_conf_compose.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.id IS '自动增加，数据库index所用，禁止在业务逻辑中使用';


--
-- Name: COLUMN laser_conf_compose.object_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.object_type IS '对象类型： 0 PageObject, 1 ExtendObject';


--
-- Name: COLUMN laser_conf_compose.object_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.object_id IS '模型object的id';


--
-- Name: COLUMN laser_conf_compose.key; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.key IS '参数名称，在json数据中，最为key使用，因此命名要符合命名规则';


--
-- Name: COLUMN laser_conf_compose.value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.value IS '参数值，以文本形式存储';


--
-- Name: COLUMN laser_conf_compose.value_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.value_type IS '0 string; 1 integer; 2 float; 3 boolean; 4 jpath; 5 range; 6 json;';


--
-- Name: COLUMN laser_conf_compose.comment; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.comment IS '参数更新说明';


--
-- Name: COLUMN laser_conf_compose."timestamp"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose."timestamp" IS '数据变更的时间戳;';


--
-- Name: COLUMN laser_conf_compose.updater; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.updater IS '提交更新账户,添加时自动生成,默认值为提交添加请求的账户名称(username + ip)';


--
-- Name: COLUMN laser_conf_compose.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose.version IS '参数版本号,添加时自动生成，新增从1开始，更新自动+1';


--
-- Name: laser_conf_compose_property; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_compose_property (
    id integer NOT NULL,
    object_type bigint NOT NULL,
    object_id bigint NOT NULL,
    property_id integer,
    key character varying(128) NOT NULL,
    value text,
    value_type character varying(16) NOT NULL,
    comment character varying(1024),
    "timestamp" timestamp without time zone NOT NULL,
    updater character varying(255) NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_compose_property OWNER TO postgres;

--
-- Name: COLUMN laser_conf_compose_property.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.id IS '自动增加，数据库index所用，禁止在业务逻辑中使用';


--
-- Name: COLUMN laser_conf_compose_property.object_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.object_type IS '对象类型： 0 PageObject, 1 ExtendObject';


--
-- Name: COLUMN laser_conf_compose_property.object_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.object_id IS '模型object的id';


--
-- Name: COLUMN laser_conf_compose_property.key; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.key IS '参数名称，在json数据中，最为key使用，因此命名要符合命名规则';


--
-- Name: COLUMN laser_conf_compose_property.value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.value IS '参数值，以文本形式存储';


--
-- Name: COLUMN laser_conf_compose_property.value_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.value_type IS '0 string; 1 integer; 2 float; 3 boolean; 4 jpath; 5 range; 6 json;';


--
-- Name: COLUMN laser_conf_compose_property.comment; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.comment IS '参数更新说明';


--
-- Name: COLUMN laser_conf_compose_property."timestamp"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property."timestamp" IS '数据变更的时间戳;';


--
-- Name: COLUMN laser_conf_compose_property.updater; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.updater IS '提交更新账户,添加时自动生成,默认值为提交添加请求的账户名称(username + ip)';


--
-- Name: COLUMN laser_conf_compose_property.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_compose_property.version IS '参数版本号,添加时自动生成，新增从1开始，更新自动+1';


--
-- Name: laser_conf_object; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_object (
    id integer NOT NULL,
    object_id bigint NOT NULL,
    key character varying(128) NOT NULL,
    value text,
    value_type character varying(16) NOT NULL,
    comment character varying(2048),
    "timestamp" timestamp without time zone NOT NULL,
    updater character varying(255) NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_object OWNER TO postgres;

--
-- Name: COLUMN laser_conf_object.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.id IS '自动增加，数据库index所用，禁止在业务逻辑中使用';


--
-- Name: COLUMN laser_conf_object.object_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.object_id IS '模型object的id';


--
-- Name: COLUMN laser_conf_object.key; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.key IS '参数名称，在json数据中，最为key使用，因此命名要符合命名规则';


--
-- Name: COLUMN laser_conf_object.value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.value IS '参数值，以文本形式存储';


--
-- Name: COLUMN laser_conf_object.value_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.value_type IS '0 string; 1 integer; 2 float; 3 boolean; 4 jpath; 5 range; 6 json;';


--
-- Name: COLUMN laser_conf_object.comment; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.comment IS '参数更新说明';


--
-- Name: COLUMN laser_conf_object."timestamp"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object."timestamp" IS '数据变更的时间戳;';


--
-- Name: COLUMN laser_conf_object.updater; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.updater IS '提交更新账户,添加时自动生成,默认值为提交添加请求的账户名称(username + ip)';


--
-- Name: COLUMN laser_conf_object.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object.version IS '参数版本号,添加时自动生成，新增从1开始，更新自动+1';


--
-- Name: laser_conf_object_model; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_object_model (
    id integer NOT NULL,
    object_id bigint NOT NULL,
    property_id bigint NOT NULL,
    order_no integer NOT NULL,
    key character varying(255) NOT NULL,
    value text,
    value_type character varying(16) NOT NULL,
    comment character varying(1024),
    updated_at timestamp without time zone NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_object_model OWNER TO postgres;

--
-- Name: COLUMN laser_conf_object_model.object_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_model.object_id IS '模型object的id';


--
-- Name: COLUMN laser_conf_object_model.property_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_model.property_id IS '模型object的属性id';


--
-- Name: COLUMN laser_conf_object_model.value_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_model.value_type IS '0 String;
1 Integer;
2 Float;
3 Boolean;
4 Datetime;
5 Array;
6 JPath;
7 Range;
8 Json;
9 Matrix;
10 Select;
11 File;
12 Table;
13 Latex;
14 Sweep;
';


--
-- Name: COLUMN laser_conf_object_model.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_model.updated_at IS '数据变更的时间戳;';


--
-- Name: laser_conf_object_property; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_object_property (
    id integer NOT NULL,
    object_id bigint NOT NULL,
    property_id integer,
    key character varying(128) NOT NULL,
    value text,
    value_type character varying(16) NOT NULL,
    comment character varying(2048),
    "timestamp" timestamp without time zone NOT NULL,
    updater character varying(255) NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_object_property OWNER TO postgres;

--
-- Name: COLUMN laser_conf_object_property.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.id IS '自动增加，数据库index所用，禁止在业务逻辑中使用';


--
-- Name: COLUMN laser_conf_object_property.object_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.object_id IS '模型object的id';


--
-- Name: COLUMN laser_conf_object_property.key; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.key IS '参数名称，在json数据中，最为key使用，因此命名要符合命名规则';


--
-- Name: COLUMN laser_conf_object_property.value; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.value IS '参数值，以文本形式存储';


--
-- Name: COLUMN laser_conf_object_property.value_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.value_type IS '0 string; 1 integer; 2 float; 3 boolean; 4 jpath; 5 range; 6 json;';


--
-- Name: COLUMN laser_conf_object_property.comment; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.comment IS '参数更新说明';


--
-- Name: COLUMN laser_conf_object_property."timestamp"; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property."timestamp" IS '数据变更的时间戳;';


--
-- Name: COLUMN laser_conf_object_property.updater; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.updater IS '提交更新账户,添加时自动生成,默认值为提交添加请求的账户名称(username + ip)';


--
-- Name: COLUMN laser_conf_object_property.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_property.version IS '参数版本号,添加时自动生成，新增从1开始，更新自动+1';


--
-- Name: laser_conf_object_script; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_object_script (
    id integer NOT NULL,
    object_id bigint NOT NULL,
    property_id bigint NOT NULL,
    type character varying(128) NOT NULL,
    script text,
    author character varying(128) NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.laser_conf_object_script OWNER TO postgres;

--
-- Name: COLUMN laser_conf_object_script.object_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_script.object_id IS '模型object的id';


--
-- Name: COLUMN laser_conf_object_script.property_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_script.property_id IS '模型object的属性id';


--
-- Name: COLUMN laser_conf_object_script.type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_script.type IS 'on_change;
on_disabled;
on_hidden;
on_validate;';


--
-- Name: COLUMN laser_conf_object_script.script; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_script.script IS '编写的脚本';


--
-- Name: COLUMN laser_conf_object_script.author; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_script.author IS '脚本编写者';


--
-- Name: COLUMN laser_conf_object_script.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_object_script.updated_at IS '添加时间';


--
-- Name: laser_conf_wizard_option; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_wizard_option (
    id integer NOT NULL,
    question_order_no integer NOT NULL,
    order_no integer NOT NULL,
    option character varying(255),
    action character varying(255),
    next_question integer,
    requests character varying(255),
    solves character varying(255),
    desc_text character varying(1024),
    desc_image character varying(255),
    updated_at timestamp without time zone NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_wizard_option OWNER TO postgres;

--
-- Name: COLUMN laser_conf_wizard_option.question_order_no; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.question_order_no IS '问题编号';


--
-- Name: COLUMN laser_conf_wizard_option.order_no; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.order_no IS '选项编号';


--
-- Name: COLUMN laser_conf_wizard_option.option; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.option IS '问题';


--
-- Name: COLUMN laser_conf_wizard_option.action; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.action IS '问题';


--
-- Name: COLUMN laser_conf_wizard_option.next_question; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.next_question IS '后继问题(编号)';


--
-- Name: COLUMN laser_conf_wizard_option.requests; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.requests IS '问题依赖项';


--
-- Name: COLUMN laser_conf_wizard_option.solves; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.solves IS '求解器依赖项';


--
-- Name: COLUMN laser_conf_wizard_option.desc_text; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.desc_text IS '选项注解';


--
-- Name: COLUMN laser_conf_wizard_option.desc_image; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.desc_image IS '选项图解';


--
-- Name: COLUMN laser_conf_wizard_option.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.updated_at IS '数据变更的时间戳;';


--
-- Name: COLUMN laser_conf_wizard_option.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_option.version IS '数据版本';


--
-- Name: laser_conf_wizard_question; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.laser_conf_wizard_question (
    id integer NOT NULL,
    order_no integer NOT NULL,
    question character varying(255) NOT NULL,
    desc_text character varying(1024),
    desc_image character varying(255),
    is_start smallint,
    is_multi_selection smallint,
    updated_at timestamp without time zone NOT NULL,
    version bigint NOT NULL
);


ALTER TABLE public.laser_conf_wizard_question OWNER TO postgres;

--
-- Name: COLUMN laser_conf_wizard_question.order_no; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.order_no IS '问题编号';


--
-- Name: COLUMN laser_conf_wizard_question.question; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.question IS '问题';


--
-- Name: COLUMN laser_conf_wizard_question.desc_text; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.desc_text IS '问题注解';


--
-- Name: COLUMN laser_conf_wizard_question.desc_image; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.desc_image IS '问题图解';


--
-- Name: COLUMN laser_conf_wizard_question.is_start; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.is_start IS '初始问题';


--
-- Name: COLUMN laser_conf_wizard_question.is_multi_selection; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.is_multi_selection IS 'option是多选模式';


--
-- Name: COLUMN laser_conf_wizard_question.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.updated_at IS '数据变更的时间戳;';


--
-- Name: COLUMN laser_conf_wizard_question.version; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.laser_conf_wizard_question.version IS '数据版本';


--
-- Name: circuit_conf_element circuit_conf_element_pkey; Type: CONSTRAINT; Schema: circuit; Owner: postgres
--

ALTER TABLE ONLY circuit.circuit_conf_element
    ADD CONSTRAINT circuit_conf_element_pkey PRIMARY KEY (id);


--
-- Name: circuit_conf_element_property circuit_conf_element_property_pkey; Type: CONSTRAINT; Schema: circuit; Owner: postgres
--

ALTER TABLE ONLY circuit.circuit_conf_element_property
    ADD CONSTRAINT circuit_conf_element_property_pkey PRIMARY KEY (id);


--
-- Name: circuit_conf_option circuit_conf_option_pkey; Type: CONSTRAINT; Schema: circuit; Owner: postgres
--

ALTER TABLE ONLY circuit.circuit_conf_option
    ADD CONSTRAINT circuit_conf_option_pkey PRIMARY KEY (id);


--
-- Name: circuit_conf_property circuit_conf_property_pkey; Type: CONSTRAINT; Schema: circuit; Owner: postgres
--

ALTER TABLE ONLY circuit.circuit_conf_property
    ADD CONSTRAINT circuit_conf_property_pkey PRIMARY KEY (id);


--
-- Name: circuit_conf_result circuit_conf_result_pkey; Type: CONSTRAINT; Schema: circuit; Owner: postgres
--

ALTER TABLE ONLY circuit.circuit_conf_result
    ADD CONSTRAINT circuit_conf_result_pkey PRIMARY KEY (id);


--
-- Name: conf_object_hook conf_object_hook_pkey; Type: CONSTRAINT; Schema: passive; Owner: postgres
--

ALTER TABLE ONLY passive.conf_object_hook
    ADD CONSTRAINT conf_object_hook_pkey PRIMARY KEY (id);


--
-- Name: conf_object conf_object_pkey; Type: CONSTRAINT; Schema: passive; Owner: postgres
--

ALTER TABLE ONLY passive.conf_object
    ADD CONSTRAINT conf_object_pkey PRIMARY KEY (id);


--
-- Name: conf_object_property conf_object_property_pkey; Type: CONSTRAINT; Schema: passive; Owner: postgres
--

ALTER TABLE ONLY passive.conf_object_property
    ADD CONSTRAINT conf_object_property_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_compose laser_conf_compose_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_compose
    ADD CONSTRAINT laser_conf_compose_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_compose_property laser_conf_compose_property_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_compose_property
    ADD CONSTRAINT laser_conf_compose_property_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_object_model laser_conf_object_model_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_object_model
    ADD CONSTRAINT laser_conf_object_model_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_object laser_conf_object_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_object
    ADD CONSTRAINT laser_conf_object_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_object_property laser_conf_object_property_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_object_property
    ADD CONSTRAINT laser_conf_object_property_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_object_script laser_conf_object_script_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_object_script
    ADD CONSTRAINT laser_conf_object_script_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_wizard_option laser_conf_wizard_option_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_wizard_option
    ADD CONSTRAINT laser_conf_wizard_option_pkey PRIMARY KEY (id);


--
-- Name: laser_conf_wizard_question laser_conf_wizard_question_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.laser_conf_wizard_question
    ADD CONSTRAINT laser_conf_wizard_question_pkey PRIMARY KEY (id);


--
-- Name: idx_conf_object_deleted_at; Type: INDEX; Schema: passive; Owner: postgres
--

CREATE INDEX idx_conf_object_deleted_at ON passive.conf_object USING btree (deleted_at);


--
-- Name: idx_conf_object_property_deleted_at; Type: INDEX; Schema: passive; Owner: postgres
--

CREATE INDEX idx_conf_object_property_deleted_at ON passive.conf_object_property USING btree (deleted_at);


--
-- Name: object_id_path_unique; Type: INDEX; Schema: passive; Owner: postgres
--

CREATE UNIQUE INDEX object_id_path_unique ON passive.conf_object_property USING btree (object_id, path);


--
-- PostgreSQL database dump complete
--

