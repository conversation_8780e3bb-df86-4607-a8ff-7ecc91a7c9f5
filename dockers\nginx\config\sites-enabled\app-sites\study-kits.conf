# Study-Kits 产品location配置片段
# 动态DNS解析
resolver 127.0.0.11 valid=30s;

# Study-Kits错误页面配置
error_page 404 /study-kits/error/404.html;
error_page 502 503 504 /study-kits/error/502.html;

# Study-Kits路径重定向 - 处理不带斜杠的请求
location = /study-kits {
    return 301 $scheme://$host/study-kits/;
}

# Study-Kits静态文件
location /study-kits/ {
    root /usr/share/nginx/www;
    try_files $uri $uri/ =404;

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}

# Study-Kits错误页面处理
location /study-kits/error/ {
    root /usr/share/nginx/www;

    ## 指令表示这个location只能被nginx内部重定向访问, 不能被外部直接访问, 只有通过 error_page、try_files 等内部重定向才能访问;
    # internal; 
    
    try_files $uri =404;
    
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
}

# Study-Kits静态资源缓存优化
location ~* ^/study-kits/.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    root /usr/share/nginx/www;
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header X-Content-Type-Options "nosniff" always;
}

# Study-Kits API服务代理
location /study-kits/api/ {
    # 使用变量进行动态解析，避免启动时DNS解析失败
    set $backend "study-api:8322";
    # 重写路径：将 /study-kits/api/ 替换为 /api/
    rewrite ^/study-kits/api/(.*)$ /api/$1 break;
    proxy_pass http://$backend;

    # 基础代理头设置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Port $server_port;

    # 代理超时设置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # 代理缓冲设置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # API特定设置
    proxy_set_header Accept-Encoding "";
    add_header X-Service-Name "study-kits-api" always;
    
    # API安全头
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;

    # 错误处理 - 当后端服务不可用时返回友好错误页面
    proxy_intercept_errors on;
}

# Study-Kits WebSocket代理路径
location /study-kits/ws {
    # 使用变量进行动态解析
    set $ws_backend "study-api:8324";
    # 重写路径：将 /study-kits/ws 替换为 /ws
    rewrite ^/study-kits/ws(.*)$ /ws$1 break;
    proxy_pass http://$ws_backend;

    # 基础代理头设置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Port $server_port;

    # WebSocket特定设置
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;

    # WebSocket超时设置
    proxy_connect_timeout 86400;
    proxy_send_timeout 86400;
    proxy_read_timeout 86400;

    # WebSocket缓冲设置
    proxy_buffering off;

    # 服务标识
    add_header X-Service-Name "study-kits-ws" always;

    # 错误处理
    proxy_intercept_errors on;
}
