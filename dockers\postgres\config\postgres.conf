# PostgreSQL 17.5 配置文件
# 基于 2GB 内存限制优化

# 连接和认证配置
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# 内存配置 (针对2GB内存限制优化)
shared_buffers = 512MB                    # 25% of RAM
effective_cache_size = 1536MB             # 75% of RAM
work_mem = 4MB                            # RAM / max_connections
maintenance_work_mem = 128MB              # RAM / 16
wal_buffers = 16MB
random_page_cost = 1.1                    # SSD优化

# 检查点配置
checkpoint_completion_target = 0.9
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_timeout = 15min

# 日志配置
logging_collector = on
log_directory = 'log'
log_filename = 'postgres-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000         # 记录超过1秒的查询
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# 性能优化
effective_io_concurrency = 200            # SSD优化
max_worker_processes = 4
max_parallel_workers_per_gather = 2
max_parallel_workers = 4
max_parallel_maintenance_workers = 2

# 自动清理配置
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = 200

# 统计信息配置
track_activities = on
track_counts = on
track_io_timing = on
track_functions = pl

# 错误报告和日志
log_min_messages = warning
log_min_error_statement = error

# 客户端连接默认值
default_text_search_config = 'pg_catalog.english'
timezone = 'Asia/Shanghai'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# 安全配置
ssl = off                                 # 容器内部通信，可关闭SSL
password_encryption = scram-sha-256

# 备份和复制配置
archive_mode = on
archive_command = 'test ! -f /var/lib/postgres/archive/%f && cp %p /var/lib/postgres/archive/%f'
max_wal_senders = 3
wal_keep_size = 1GB

# 锁配置
deadlock_timeout = 1s
lock_timeout = 30s
statement_timeout = 0                     # 0表示无限制

# 其他优化
shared_preload_libraries = 'pg_stat_statements'
pg_stat_statements.max = 10000
pg_stat_statements.track = all
