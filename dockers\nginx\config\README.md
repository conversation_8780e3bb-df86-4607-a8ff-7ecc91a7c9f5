# Nginx 模块化配置架构

## 📋 概述

本配置采用模块化架构设计，专为产品验证平台打造，既保持简洁性又具备强大的扩展能力。支持快速添加、启用、禁用和管理多个产品服务。

## 🏗️ 目录结构

```
config/
├── nginx.conf                    # 主配置文件（简洁）
├── conf.d/                       # 通用配置片段
│   └── ssl.conf                  # SSL通用配置
├── sites-available/              # 可用站点配置(暂停使用)
├── sites-enabled/                # 启用的站点
│   ├── app.conf                  # https://app.heivy.cn 所有产品的二级根地址, 黑羽公司的产品能力简介(不宣传具体产品)宣传页;
│   ├── certimate.conf            # https://cert.heivy.cn 产品验证平台中的证书 LetsEncrypt 管理服务
│   └── ......                    # https://app.heivy.cn/xxx  具体产品的主页
└── README.md                     # 本文档
```

## 📝 配置原则

- 这些站点配置文件，无论对应的后端服务是否启动，是否工作正常，都不能影响 nginx 正常启动和转发。对应的产品前端显示正确的错误码或错误页面即可;
- 服务器上的配置文件要清晰干净，用不到的文件要注意清理;


## 🚀 使用方法

### 常用命令
```bash
## 创建软链接启用产品
ln -sf ../sites-available/my-product.conf sites-enabled/my-product.conf

# 删除软链接
rm sites-enabled/my-product.conf

# 重启服务
docker-compose restart nginx

# 检查nginx配置语法
docker exec nginx_service nginx -t

# 重新加载配置（无需重启）
docker exec nginx_service nginx -s reload
```


