# ---> Go
# Compiled Object files, Static and Dynamic libs (Shared Objects)
*.o
*.a
*.so

# Folders
_obj
_test

images
bin

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

*.exe
*.test
*.prof


images
bin
.DS_Store
logs
go-caches
release
*.local.yml
.env.local
*.local.conf

!config.local.yml

# Backend services (managed separately)
services/go-apps/serv-api/
services/go-apps/study-api/
services/go-apps/words-api/
