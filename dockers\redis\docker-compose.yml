services:
  redis:
    image: redis:7.2
    container_name: hv-apps-redis
    ports:
      - "16379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    networks:
      - app_network
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis123456", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

networks:
  app_network:
    external: true

volumes:
  redis_data: