-- PostgreSQL 初始化脚本
-- 配置数据库和用户权限

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 设置默认搜索路径
ALTER DATABASE postgres SET search_path TO public;

-- 创建示例用户（可选）
-- CREATE USER app_user WITH PASSWORD 'your_password_here';
-- GRANT CONNECT ON DATABASE postgres TO app_user;
-- GRANT USAGE ON SCHEMA public TO app_user;
-- GRANT CREATE ON SCHEMA public TO app_user;

-- 显示当前数据库信息（用于调试）
SELECT current_database(), current_user, version();

-- 显示已安装的扩展
SELECT extname, extversion FROM pg_extension;

-- 使用说明：
-- 1. 将需要初始化的 SQL 文件放在此目录下
-- 2. 文件将按字母顺序执行
-- 3. 建议使用数字前缀命名，如：01_create_database.sql, 02_create_tables.sql
--
-- 示例：
-- CREATE DATABASE your_database_name 
--   WITH ENCODING 'UTF8' 
--   LC_COLLATE='en_US.utf8' 
--   LC_CTYPE='en_US.utf8' 
--   TEMPLATE=template0;
