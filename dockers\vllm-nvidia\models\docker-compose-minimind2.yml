# MiniMind2-V 模型专用配置覆盖
# 此文件覆盖基础配置中的特定参数

services:
  vllm-server:
    container_name: vllm_minimind2_server
    environment:
      - MODEL_NAME=MiniMind2-V
      - MODEL_PATH=/models/MiniMind2-V
      - MAX_MODEL_LEN=2048
      - TENSOR_PARALLEL_SIZE=1
      - GPU_MEMORY_UTILIZATION=0.9
    # MiniMind2-V 专用启动命令 - 使用容器默认入口点
    command: [
      "--model", "/models/MiniMind2-V",
      "--host", "0.0.0.0",
      "--port", "18080",
      "--trust-remote-code",
      "--max-model-len", "2048",
      "--tensor-parallel-size", "1",
      "--gpu-memory-utilization", "0.9",
      "--api-key", "your-api-key-here",
      "--served-model-name", "MiniMind2-V"
    ]
