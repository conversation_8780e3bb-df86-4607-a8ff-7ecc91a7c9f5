services:
  nginx:
    image: nginx:1.28.0
    container_name: hv-apps-nginx
    ports:
      - "80:80"
      - "443:443"

    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/conf.d:/etc/nginx/conf.d:ro
      - ./config/sites-available:/etc/nginx/sites-available:ro
      - ./config/sites-enabled:/etc/nginx/sites-enabled:ro
      - ./certs/pro:/etc/ssl/nginx:ro
      - /home/<USER>/www:/usr/share/nginx/www
    networks:
      - app_network
    restart: unless-stopped

networks:
  app_network:
    external: true