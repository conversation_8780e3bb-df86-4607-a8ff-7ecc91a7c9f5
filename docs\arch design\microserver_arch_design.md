# 后端总体架构设计

## 1. 概述

### 1.1 设计理念

因为很多产品是验证型的，验证成功的话，会将该产品独立出去，并给予更完备的配置。如果产品验证失败，可以很轻松的下架。因此，以尽量小成本，满足所有产品开发验证的需要，设计了这个架构。

### 1.2 适用范围

本文档只描述后端的总体架构设计, 前端的设计不在本文档设计范围, 有另外的文档专门描述.

## 2. 产品类型

- **Web型**: 研发垂直领域的 SaaS型、工具性、AI助手型等产品;
- **手机APP**: 研发好用的，能解决用户某一特定问题的移动应用APP;
- **小游戏**: H5小程序、小游戏类的产品;
- **手机游戏**: 使用unity制作的手机游戏;

## 3. 后端系统架构

### 3.1 基础服务

- **nginx**: 所有产品API通过nginx统一路由, 跨域处理, 限流保护等;
- **postgres**: RDB数据服务，存放结构化数据;
- **redis**: 缓存服务, 提高访问速度
- **CDN**: 腾讯云CDN服务(购买)，小尺寸静态资源（CSS、JS、小图标等）- 快速分发;
- **OSS**: 腾讯云COS对象存储服务(购买)，大文件存储（图片、音频、视频）- 成本优化;

### 3.2 产品/功能服务

- **serv-api**: 为产品 `greet` 的api服务;
- **words-api**: 为产品 `words` 的api服务;

### 3.3 服务部署

- **server**: 腾讯云服务器(购买)
- **docker**: 容器化部署

### 3.4 产品运营

- **产品内容**: 通过n8n+AI的方式，为产品填充运营内容;
- **数据隔离**: 产品间用数据库隔离, 产品内模块schema隔离;
- **数据统计**: 使用第三方数据平台，获取用户使用数据, 分析产品功能和用户画像，商家待补...;
- **服务监控**: 待补...


## 4 后端代码架构

### 4.1 go-core 基础库
为各个产品服务代码, 相同的功能, 提供统一的处理方法.

### 4.2 go-apps 产品服务

#### 4.2.1 serv-api 
为产品 `greet` 的api服务;

#### 4.2.2 words-api 
为产品 `words` 的api服务;




