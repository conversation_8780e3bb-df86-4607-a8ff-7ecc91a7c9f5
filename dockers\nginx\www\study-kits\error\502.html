<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>502 - 服务暂时不可用 | Study-Kits</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .error-code {
            font-size: 120px;
            font-weight: 700;
            color: #ff6b6b;
            line-height: 1;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
        }
        
        .error-title {
            font-size: 28px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
        }
        
        .error-message {
            font-size: 16px;
            color: #718096;
            line-height: 1.6;
            margin-bottom: 40px;
        }
        
        .error-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #ff6b6b;
            color: white;
        }
        
        .btn-primary:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }
        
        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #edf2f7;
            transform: translateY(-2px);
        }
        
        .service-status {
            margin-top: 30px;
            padding: 20px;
            background: #fff5f5;
            border-radius: 12px;
            border-left: 4px solid #ff6b6b;
        }
        
        .status-title {
            font-weight: 600;
            color: #c53030;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .status-message {
            font-size: 13px;
            color: #718096;
            line-height: 1.5;
        }
        
        .service-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #a0aec0;
        }
        
        .service-name {
            font-weight: 600;
            color: #ff6b6b;
        }
        
        .retry-info {
            margin-top: 20px;
            font-size: 12px;
            color: #a0aec0;
        }
        
        @media (max-width: 480px) {
            .error-container {
                padding: 40px 20px;
            }
            
            .error-code {
                font-size: 80px;
            }
            
            .error-title {
                font-size: 24px;
            }
            
            .error-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">502</div>
        <h1 class="error-title">服务暂时不可用</h1>
        <p class="error-message">
            Study-Kits服务正在维护或暂时无法响应。<br>
            我们正在努力恢复服务，请稍后再试。
        </p>
        
        <div class="service-status">
            <div class="status-title">🔧 服务状态</div>
            <div class="status-message">
                后端API服务暂时不可用，可能正在进行系统维护或更新。
                通常这种情况会在几分钟内自动恢复。
            </div>
        </div>
        
        <div class="error-actions">
            <a href="javascript:location.reload()" class="btn btn-primary">重新加载</a>
            <a href="/study-kits/" class="btn btn-secondary">返回首页</a>
        </div>
        
        <div class="retry-info">
            如果问题持续存在，请联系技术支持
        </div>
        
        <div class="service-info">
            <div class="service-name">Study-Kits</div>
            <div>智能学习工具平台</div>
        </div>
    </div>
</body>
</html>
