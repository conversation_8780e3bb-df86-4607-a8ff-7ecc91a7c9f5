services:
  postgres:
    image: postgres:17.5
    container_name: hv-apps-postgres
    ports:
      - "15432:5432"
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_ROOT_PASSWORD}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_DB=postgres
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
      # 不创建默认数据库，未来手动添加
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgres/data
      - postgres_backup:/var/backups/postgres
      - ./config/postgres.conf:/etc/postgres/postgres.conf:ro
      - ./init:/docker-entrypoint-initdb.d:ro
    networks:
      - app_network
    restart: unless-stopped
    # 健康检查配置
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 资源限制配置
    deploy:
      resources:
        limits:
          memory: 2g
        reservations:
          memory: 512m
    # 安全配置
    security_opt:
      - no-new-privileges:true
    # 使用postgres用户启动
    user: postgres
    # 自定义启动命令，使用自定义配置文件
    command: >
      postgres
      -c config_file=/etc/postgres/postgres.conf

networks:
  app_network:
    external: true

volumes:
  postgres_data:
  postgres_backup:
