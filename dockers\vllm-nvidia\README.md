# vLLM 多模型管理指南

本指南介绍如何使用配置覆盖模式管理不同的 vLLM 模型。

## 📁 文件结构

```
dockers/vllm-nvidia/
├── docker-compose.yml              # 通用基础配置
├── models/                         # 模型专用配置目录
│   ├── docker-compose-qwen3_0.6B.yml   # Qwen3-0.6B 专用配置
│   ├── docker-compose-minimind2.yml    # MiniMind2-V 专用配置（实验性）
│   └── docker-compose-template.yml     # 新模型配置模板
├── vllm.sh                         # 统一管理脚本
└── README.md                       # 本说明文档
```

## 🎯 配置覆盖模式

新架构使用 Docker Compose 的配置覆盖功能：
- `docker-compose.yml` 包含所有模型的通用配置
- `models/docker-compose-[模型名].yml` 包含特定模型的覆盖配置
- 启动时合并两个配置文件：`docker-compose -f docker-compose.yml -f models/docker-compose-[模型名].yml up -d`

## 🚀 快速开始

### 1. 启动 vLLM 服务

```bash
# 启动服务（推荐方式）
./vllm.sh up

# 或者直接运行（默认执行 up 命令）
./vllm.sh
```

### 2. 管理服务

```bash
# 停止服务（保留容器）
./vllm.sh stop

# 停止并清理服务（删除容器和网络）
./vllm.sh down

# 列出可用模型
./vllm.sh list

# 显示帮助信息
./vllm.sh help
```

### 3. 切换模型

编辑 `vllm.sh` 脚本中的模型变量：
```bash
# 修改这一行来切换模型
MODEL_NAME="qwen3_0.6B"  # 改为其他模型名
```

### 4. 查看运行状态

```bash
# 查看容器状态
docker ps --filter "name=vllm"

# 查看日志（以 qwen3_0.6B 为例）
docker-compose -f docker-compose.yml -f models/docker-compose-qwen3_0.6B.yml logs -f
```

## 🛠️ 脚本使用说明

### vllm.sh 脚本

统一管理脚本，支持多种操作命令：

```bash
# 查看帮助信息
./vllm.sh help

# 启动服务
./vllm.sh up

# 停止服务
./vllm.sh stop

# 停止并清理服务
./vllm.sh down

# 列出可用模型
./vllm.sh list
```

### 脚本功能详解

| 命令 | 功能 | 说明 |
|------|------|------|
| `up` | 启动服务 | 自动停止现有服务，启动新服务，等待就绪 |
| `stop` | 停止服务 | 停止容器但保留资源，可快速重启 |
| `down` | 清理服务 | 停止并删除容器、网络等所有资源 |
| `list` | 列出模型 | 显示可用模型配置和当前配置 |
| `help` | 显示帮助 | 显示完整的使用说明 |

### 切换模型

编辑 `vllm.sh` 脚本中的模型变量：
```bash
# 修改这一行来切换模型
MODEL_NAME="qwen3_0.6B"  # 改为其他模型名
```

支持的模型：
- `qwen3_0.6B` - Qwen3-0.6B 模型（推荐）
- `minimind2` - MiniMind2-V 模型（实验性支持）

## ➕ 添加新模型

### 步骤1: 下载模型

将新模型下载到 `D:\.llm-models\` 目录下，例如：
```
D:\.llm-models\
├── Qwen\
│   └── Qwen3-0.6B\       # 推荐模型
├── MiniMind2-V\          # 实验性模型
└── new-model\            # 新模型
```

### 步骤2: 创建配置文件

1. 复制模板文件：
   ```bash
   cp models/docker-compose-template.yml models/docker-compose-newmodel.yml
   ```

2. 编辑新配置文件，替换以下占位符：
   - `[MODEL_NAME]` → 实际模型名（如 `newmodel`）
   - 根据模型大小调整参数（见下方参数说明）
   - ⚠️ **重要**：确保 `command` 中不包含 `"python3", "-m", "vllm.entrypoints.openai.api_server"`

### 步骤3: 启动新模型

1. 编辑 `vllm.sh` 脚本：
   ```bash
   MODEL_NAME="newmodel"  # 修改为新模型名
   ```

2. 运行启动脚本：
   ```bash
   ./vllm.sh up
   ```

## ⚙️ 配置参数说明

### 关键参数调整指南

| 参数 | 小模型 (≤7B) | 中等模型 (7B-30B) | 大模型 (≥30B) |
|------|-------------|------------------|---------------|
| `MAX_MODEL_LEN` | 2048-4096 | 2048-8192 | 1024-4096 |
| `TENSOR_PARALLEL_SIZE` | 1 | 1-2 | 2-4 |
| `GPU_MEMORY_UTILIZATION` | 0.9 | 0.8-0.9 | 0.7-0.8 |
| `memory` | 20g | 30g | 40g+ |
| `start_period` | 120s | 180s | 300s |

### 端口管理

- 默认端口：`18080`
- 如需同时运行多个模型，请修改端口避免冲突：
  ```yaml
  ports:
    - "18081:18080"  # 第二个模型使用 18081
  ```

## 🔄 模型切换流程

### 单模型运行（推荐）

1. 编辑 `vllm.sh` 脚本中的模型变量：
   ```bash
   MODEL_NAME="qwen3_0.6B"  # 修改为目标模型名
   ```

2. 运行启动脚本（会自动停止现有服务）：
   ```bash
   ./vllm.sh up
   ```

### 多模型同时运行

如需同时运行多个模型：
1. 修改不同模型配置文件中的端口号避免冲突
2. 确保有足够的 GPU 内存
3. 手动使用不同的配置文件启动：
   ```bash
   docker-compose -f docker-compose.yml -f models/docker-compose-qwen3_0.6B.yml up -d
   docker-compose -f docker-compose.yml -f models/docker-compose-model2.yml up -d
   ```

## 🛠️ 故障排除

### 常见问题

1. **端口冲突**
   ```
   Error: Port 18080 is already in use
   ```
   解决：停止其他模型或修改端口号

2. **GPU 内存不足**
   ```
   CUDA out of memory
   ```
   解决：降低 `GPU_MEMORY_UTILIZATION` 或 `MAX_MODEL_LEN`

3. **模型加载失败**
   ```
   Model not found
   ```
   解决：检查模型路径和文件完整性

4. **⚠️ 启动命令错误（重要）**
   ```
   api_server.py: error: unrecognized arguments: -m vllm.entrypoints.openai.api_server
   ```
   **错误原因**：vLLM 容器的入口点已经是 `python3 -m vllm.entrypoints.openai.api_server`，
   在 command 中不应该重复指定这部分。

   **错误配置示例**：
   ```yaml
   command: [
     "python3", "-m", "vllm.entrypoints.openai.api_server",  # ❌ 错误：重复指定入口点
     "--model", "Qwen/Qwen3-0.6B",
     "--host", "0.0.0.0",
     # ...其他参数
   ]
   ```

   **正确配置**：
   ```yaml
   command: [
     "--model", "Qwen/Qwen3-0.6B",        # ✅ 正确：直接指定参数
     "--host", "0.0.0.0",
     "--port", "18080",
     "--trust-remote-code",
     # ...其他参数
   ]
   ```

   **解决方法**：从 command 中移除 `"python3", "-m", "vllm.entrypoints.openai.api_server"` 部分，
   只保留 vLLM 的启动参数。

### 日志查看

```bash
# 查看容器状态
docker ps --filter "name=vllm"

# 查看实时日志（以 qwen3_0.6B 为例）
docker-compose -f docker-compose.yml -f models/docker-compose-qwen3_0.6B.yml logs -f

# 查看详细错误
docker-compose -f docker-compose.yml -f models/docker-compose-qwen3_0.6B.yml logs --tail=50

# 或者直接查看容器日志
docker logs vllm_qwen3_0.6B_server -f
```

## 📊 性能监控

### GPU 使用情况

```bash
# 查看 GPU 状态
nvidia-smi

# 持续监控
nvidia-smi -l 1
```

### API 测试

```bash
# 健康检查
curl http://localhost:18080/health

# 模型信息
curl http://localhost:18080/v1/models

# 简单测试
curl -X POST http://localhost:18080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key-here" \
  -d '{
    "model": "Qwen/Qwen3-0.6B",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'
```

## 📝 最佳实践

1. **资源管理**
   - 单 GPU 建议只运行一个大模型
   - 小模型可以适当提高 GPU 利用率

2. **配置管理**
   - 使用配置覆盖模式，保持基础配置和模型配置分离
   - 为每个模型创建专用配置文件在 `models/` 目录下
   - 使用有意义的文件名和容器名

3. **监控维护**
   - 定期检查日志和性能
   - 及时清理无用的容器和镜像
   - 使用 `docker system prune` 清理无用资源

4. **备份配置**
   - 定期备份配置文件
   - 记录重要的参数调整
   - 版本控制配置文件变更

---

## 📋 常用命令速查

```bash
# 启动服务
./vllm.sh up

# 停止服务
./vllm.sh stop

# 停止并清理服务
./vllm.sh down

# 列出可用模型
./vllm.sh list

# 查看运行状态
docker ps --filter "name=vllm"

# 查看日志
docker logs vllm_qwen3_0.6B_server -f

# 健康检查
curl http://localhost:18080/health

# 列出可用模型配置
ls models/docker-compose-*.yml | grep -v template
```
