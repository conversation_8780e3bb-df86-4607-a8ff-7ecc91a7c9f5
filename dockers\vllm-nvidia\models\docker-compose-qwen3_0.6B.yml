# Qwen3-0.6B 模型专用配置
# 基于 docker-compose.yml 的基础配置，覆盖特定参数

services:
  vllm-server:
    container_name: vllm_qwen3_0.6B_server
    environment:
      - MODEL_NAME=Qwen/Qwen3-0.6B
      - MODEL_PATH=Qwen/Qwen3-0.6B
      - MAX_MODEL_LEN=8192                    # Qwen3-0.6B 支持较长上下文
      - TENSOR_PARALLEL_SIZE=1                # 0.6B 小模型单GPU即可
      - GPU_MEMORY_UTILIZATION=0.7            # 小模型可以降低显存使用
      - VLLM_API_KEY=4aa6a06d-0810-46dd-93a4-7042d2b5c79e  # 这个Key是我随便生成的
    # 小模型资源配置
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1                        # 单GPU
              capabilities: [gpu]
        limits:
          memory: 16g                         # 小模型内存需求较低
    # 健康检查 - 小模型启动较快
    healthcheck:
      start_period: 60s                       # 小模型启动快，减少等待时间
    # Qwen3-0.6B 专用启动命令
    command: [
      "--model", "Qwen/Qwen3-0.6B",
      "--host", "0.0.0.0",
      "--port", "18080",
      "--trust-remote-code",
      "--max-model-len", "8192",
      "--tensor-parallel-size", "1",
      "--gpu-memory-utilization", "0.7",
      "--api-key", "4aa6a06d-0810-46dd-93a4-7042d2b5c79e",   # 这个KEY要和前边的一致
      "--served-model-name", "Qwen3-0.6B"
    ]
