version: '3.8'
services:
  mysql:
    image: mysql:8.4.5
    container_name: hv-apps-mysql
    ports:
      - "${MYSQL_HOST_PORT}:${MYSQL_CONTAINER_PORT}"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_CHARACTER_SET_SERVER=${MYSQL_CHARSET}
      - MYSQL_COLLATION_SERVER=${MYSQL_COLLATION}
      # 不创建默认数据库，未来手动添加
    env_file:
      - .env
    volumes:
      - /home/<USER>/volumes/mysql/data:/var/lib/mysql
      - /home/<USER>/volumes/mysql/backup:/var/backups/mysql
      - ./config/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./init:/docker-entrypoint-initdb.d:ro
    networks:
      - app_network
    restart: unless-stopped
    # 健康检查配置
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 资源限制配置
    deploy:
      resources:
        limits:
          memory: ${MYSQL_MEMORY_LIMIT}
        reservations:
          memory: 512m
    # 安全配置
    security_opt:
      - no-new-privileges:true
    # user: mysql  # 注释掉用户设置，使用root用户启动

networks:
  app_network:
    external: true