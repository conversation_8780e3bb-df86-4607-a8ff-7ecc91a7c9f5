# 编译Linux的Docker环境


## 介绍

本地开发的代码在Mac/Windows等环境下调试完成后，需要编译成Linux环境下可运行的二进制文件。
本目录下的docker-compose配置文件用于创建一个Linux编译环境的容器，它可以：

- 提供一个干净的Ubuntu Linux环境
- 预装了Go编译环境和必要的开发工具
- 通过挂载卷共享本地代码
- 缓存Go模块，避免重复下载

## 使用命令

- 构建容器
```bash
docker-compose build
```

- 启动容器
```bash
docker-compose up -d
```

- 进入容器
```bash
docker-compose exec linux-builder bash
```

- 编译程序
```bash
cd /app/services/go-apps/words-api
go mod tidy
make build
```

- 退出容器
```bash
exit
```

- 停止容器
```bash
## 只停止，不删除
docker-compose stop

## 停止并删除 container, network, volumes
docker-compose down
```
- 打包程序
```bash
cd /app/services/go-apps/words-api
make release
```

- 上传程序
退回到宿主机，上传tar.gz文件
```bash
scp -r ./words-api_0.0.3_x86_64.tar.gz ubuntu@122.51.213.70:/home/<USER>/services/
```