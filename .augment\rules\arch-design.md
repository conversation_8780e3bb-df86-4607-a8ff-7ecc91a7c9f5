---
alwaysApply: true
---

# 软件架构设计规则 (Architecture Design Rules)

## 1. 工程目录规则 (Project Directory Rules)

### 1.1 根目录结构
```
project-root/
├── src/                    # 源代码目录
│   ├── go-core/           # 核心Go模块
│   └── services/          # 微服务目录
├── dockers/               # Docker配置文件
├── scripts/               # 部署和运维脚本
├── networks/              # 网络配置
├── docs/                  # 项目文档
└── .augment/             # Augment配置
```

### 1.2 Go项目结构规范
```
service-name/
├── cmd/                   # 应用程序入口点
├── internal/              # 私有应用程序代码
│   ├── handler/          # HTTP处理器
│   ├── service/          # 业务逻辑层
│   ├── repository/       # 数据访问层
│   └── model/            # 数据模型
├── pkg/                   # 可被外部应用程序使用的库代码
├── api/                   # API定义文件
├── configs/               # 配置文件
├── deployments/           # 部署配置
├── test/                  # 测试文件
├── go.mod                 # Go模块文件
└── README.md             # 服务说明文档
```

### 1.3 目录命名规范
- 使用小写字母和连字符分隔
- 目录名应简洁明了，体现功能
- 避免使用缩写，除非是广泛认知的标准缩写
- 服务目录以功能域命名，如 `user-service`, `order-service`

## 2. 软件架构核心规则 (Core Architecture Rules)

### 2.1 微服务架构原则
- **单一职责原则**: 每个服务只负责一个业务域
- **服务自治**: 每个服务拥有独立的数据库和部署周期
- **去中心化**: 避免单点故障，服务间通过API通信
- **容错设计**: 实现熔断器、重试机制和降级策略

### 2.2 分层架构设计
```
┌─────────────────┐
│   Presentation  │  # API层/控制器层
├─────────────────┤
│    Business     │  # 业务逻辑层
├─────────────────┤
│   Persistence   │  # 数据持久化层
├─────────────────┤
│  Infrastructure │  # 基础设施层
└─────────────────┘
```

### 2.3 依赖管理原则
- **依赖倒置**: 高层模块不应依赖低层模块，都应依赖抽象
- **接口隔离**: 使用接口定义服务边界
- **循环依赖禁止**: 严禁模块间出现循环依赖
- **最小依赖**: 只引入必要的外部依赖

### 2.4 数据一致性策略
- **最终一致性**: 分布式事务采用最终一致性模型
- **事件驱动**: 使用事件溯源和CQRS模式
- **补偿机制**: 实现Saga模式处理分布式事务

## 3. Go编码规则 (Go Coding Rules)

### 3.1 命名规范
- **包名**: 小写单词，简洁明了，避免下划线
- **函数名**: 驼峰命名，公开函数首字母大写
- **变量名**: 驼峰命名，私有变量首字母小写
- **常量名**: 全大写，下划线分隔
- **接口名**: 以 `er` 结尾，如 `Reader`, `Writer`

### 3.2 代码组织
```go
// 文件头部注释
package main

import (
    // 标准库
    "context"
    "fmt"
    
    // 第三方库
    "github.com/gin-gonic/gin"
    
    // 本地包
    "project/internal/service"
)
```

### 3.3 错误处理
- 使用 `error` 接口返回错误
- 错误信息应具体且有意义
- 使用 `fmt.Errorf` 包装错误上下文
- 在适当层级记录错误日志

### 3.4 并发编程
- 优先使用 channel 进行 goroutine 通信
- 使用 `context.Context` 管理请求生命周期
- 避免共享内存，通过通信共享内存
- 合理使用 `sync` 包的同步原语

### 3.5 性能优化
- 避免不必要的内存分配
- 使用对象池复用昂贵对象
- 合理使用缓存机制
- 定期进行性能分析和优化

## 4. 容器化规则 (Containerization Rules)

### 4.1 Docker最佳实践
- 使用多阶段构建减小镜像体积
- 选择合适的基础镜像
- 合理设置健康检查
- 使用非root用户运行应用

### 4.2 服务发现与配置
- 使用环境变量进行配置管理
- 实现优雅关闭机制
- 配置外部化，避免硬编码
- 支持配置热更新

## 5. 质量保证规则 (Quality Assurance Rules)

### 5.1 测试策略
- 单元测试覆盖率不低于80%
- 集成测试覆盖关键业务流程
- 使用表驱动测试提高测试效率
- 实现端到端测试验证系统功能

### 5.2 代码审查
- 所有代码变更必须经过代码审查
- 使用静态代码分析工具
- 遵循代码风格指南
- 及时修复技术债务

### 5.3 监控与日志
- 实现结构化日志记录
- 添加关键指标监控
- 实现分布式链路追踪
- 设置合理的告警机制

---

*本规则文档将随着项目发展持续更新和完善*
