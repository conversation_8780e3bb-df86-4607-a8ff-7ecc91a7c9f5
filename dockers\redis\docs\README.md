# Redis 生产环境服务

这是一个基于Docker的Redis服务配置，适用于生产环境。

## 配置说明

- Redis版本: 7.2
- 端口映射: 16379(宿主机):6379(容器)
- 密码: redis123456
- 最大内存: 2GB
- 内存策略: allkeys-lru (当内存不足时，删除最近最少使用的键)
- 持久化: 启用AOF和RDB双重持久化

## 目录结构

```
redis/
  ├── config/
  │   └── redis.conf    # Redis配置文件
  ├── data/             # 数据持久化目录
  ├── docker-compose.yml
  └── README.md
```

## 使用方法

### 启动服务

```bash
cd /home/<USER>/redis-setup
docker-compose up -d
```

### 验证服务状态

```bash
docker-compose ps
docker logs redis_service
```

### 连接到Redis

```bash
# 本地连接
docker exec -it redis_service redis-cli -a redis123456

# 远程连接
redis-cli -h 192.168.11.165 -p 16379 -a redis123456
```

### 停止服务

```bash
docker-compose down
```

## 性能优化

本配置已包含以下性能优化：

1. 系统参数优化 (somaxconn, overcommit_memory)
2. 文件描述符限制提升
3. 资源限制配置 (CPU和内存)
4. 惰性释放内存
5. 数据结构优化

## 监控与维护

可以通过以下命令查看Redis的运行状态：

```bash
docker exec -it redis_service redis-cli -a redis123456 info
```

## 备份数据

```bash
# 手动触发RDB备份
docker exec -it redis_service redis-cli -a redis123456 bgsave

# 备份数据文件
cp -r /home/<USER>/redis-setup/../redis/data /backup/redis-$(date +%Y%m%d)
``` 