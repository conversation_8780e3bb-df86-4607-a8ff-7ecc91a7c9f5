# 安装 Ubuntu 22.04 服务器版

## 1. 安装系统
下载： 
    https://mirrors.huaweicloud.com/repository/ubuntu-releases/22.04.5/

安装过程参考:
    https://blog.csdn.net/shuchaoyang/article/details/144143812

```bash
# 改密码:
sudo passwd root


# 安装net-tools
sudo apt install net-tools

# 设置网络代理 Proxy
vim ~/.bashrc
# 末端添加
export http_proxy="http://192.168.91.28:7897"
export https_proxy="http://192.168.91.28:7897"
# 刷新.bashrc
source ~/.bashrc

```
    

### 1.2 出现问题

#### 1.2.1 问题1: Daemons using outdated libraries

```bash
# (1)安装 needrestart
# 默认情况下，needrestart 已经安装在 Ubuntu 中。如果没有，您可以使用以下命令安装。
sudo apt install needrestart 

# (2)显示所有应重新启动的服务
# 使用以下命令行显示应重新启动哪些服务。
sudo needrestart -b

# (3)检查并重新启动
# 现在检查并重新启动服务应使用下面编写的命令重新启动。
sudo needrestart -u NeedRestart::UI::stdio -r l
sudo needrestart -u NeedRestart::UI::stdio -r a

# (4)重启服务器
sudo reboot

# (5)确认
# 重启服务器后，确认重新启动的服务的变化
sudo needrestart -b

# 再次确认一下needrestart的状态。
sudo needrestart -u NeedRestart::UI::stdio -r l

```


## 2. 安装 Docker

### 2.1 安装 Docker

```bash
# 更新软件包索引
sudo apt-get update

# 允许APT使用HTTPS
sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker的稳定版本仓库
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null


# 再次更新软件包索引
sudo apt-get update

# 安装Docker CE（社区版）
sudo apt-get install -y docker-ce


# 重要: 将当前用户ubuntu加入docker用户组
sudo usermod -aG docker ubuntu
# 重新登录或刷新组权限
newgrp docker

# 查看版本
docker info

Client: Docker Engine - Community
Version:    28.0.4
Context:    default
Debug Mode: false
..



```

### 2.2 修改配置

```bash
# 创建docker数据目录
mkdir -p ~/.docker

# 创建文件
sudo mkdir -p /etc/docker 
sudo touch /etc/docker/daemon.json
sudo vi /etc/docker/daemon.json

# 内容如下
{
    "registry-mirrors": [
        "https://docker.1ms.run",
        "https://lispy.org"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "50m",
        "max-file": "3"
    },
    "data-root": "/home/<USER>/.docker",
    "insecure-registries": ["mirrors.aliyun.com"]
}

## 参数解释：
## 
## registry-mirrors: 配置镜像加速器，加快镜像拉取速度。由于dockerhub镜像无法直接下载，这里提供了国内加速器，可以直接下载镜像，非常方便。
## log-driver 和 log-opts: 控制容器日志的存储和管理。这个非常有必要，docker长期运行就产生大量的日志，导致磁盘占满。
## data-root: 更改 Docker 数据的存储路径。主要是为了将目录改为数据磁盘所在的目录，因为那块硬盘空大。
## insecure-registries: 允许连接不安全的注册表。主要是配置私有仓库的，比如：harbor


# 重启docker
sudo systemctl restart docker

# 设置开机自启动
sudo systemctl enable docker

# 设置权限
sudo chown ubuntu:ubuntu -R .docker

# 测试下载dockerhub镜像
docker run hello-world

```


### 2.3 安装 Docker Compose

```bash

# 安装 docker-compose-plugin
sudo apt install docker-compose-plugin

# 验证安装
docker compose version
## Docker Compose version v2.36.2

sudo apt install docker-compose
docker-compose version

```


### 2.4 为将来的Docker服务预创建一个network

```bash
# 将会有多个Docker服务需要相互通信，这种情况下建议保留并正确配置networks;
docker network create app_network

# 查询当前有哪些 networks;
docker network ls
```



## 3. 配置SSH密钥免密登录(可选)

```bash
# 本地windows
cat ~/.ssh/id_rsa.pub
# 复制输出的公钥内容，然后在服务器上:
vi ~/.ssh/authorized_keys
# 将公钥内容粘贴到文件末尾，保存退出

# 设置正确权限
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys

```

## 4. 安装监控工具 btop++

### 4.1 btop++ 监控cpu\内存\磁盘\网络

```bash


# btop++ 是htop的现代化替代品
sudo snap install btop   # 安装最新版

# 查看命令
btop
# 完整查看命令
btop++

```

### 4.2 iotop-c 监控磁盘IO(读写)

```bash
# 安装
sudo apt install iotop-c -y

# 查看版本
iotop-c -v
>> iotop 1.21

# 使用(需要管理员权限)
sudo iotop-c

## 只显示有I/O活动的进程
sudo iotop-c -o

## 每3秒刷新一次
sudo iotop-c -d 3

## 按总I/O排序（默认）
sudo iotop-c -o

## 按读取速度排序
sudo iotop-c -P

```