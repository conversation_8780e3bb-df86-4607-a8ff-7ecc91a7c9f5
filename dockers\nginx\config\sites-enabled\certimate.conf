# Certimate 产品配置
# 产品验证平台中的证书管理服务

# Certimate HTTPS服务 (端口 443 -> 8910)
server {
    listen 443 ssl;
    server_name cert.heivy.cn;

    # 动态DNS解析
    resolver 127.0.0.11 valid=30s;

    # 引入SSL配置
    include /etc/nginx/conf.d/ssl.conf;

    # API服务代理
    location / {
        # **********：这是docker网络 app_network 的网关地址，从容器内访问宿主机的标准方式；
        # 8910：certimate实际监听的端口;
        set $backend "**********:8910";
        proxy_pass http://$backend;

        # 基础代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;

        # 代理超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 代理缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;

        # API特定设置
        proxy_set_header Accept-Encoding "";
        add_header X-Service-Name "certimate" always;
    }
}

