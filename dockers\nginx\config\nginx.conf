# 产品验证平台 - Nginx 主配置文件
# 模块化架构设计，支持快速扩展新产品

events {
    worker_connections 1024;
}

http {
    # 基础设置
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # WebSocket升级映射
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 引入通用配置片段
    include /etc/nginx/conf.d/*.conf;

    # 引入启用的站点配置
    include /etc/nginx/sites-enabled/*.conf;
}