#!/bin/bash

# vLLM 模型启动脚本
# 使用配置覆盖模式启动不同的模型

# ===========================================
# 配置区域 - 在这里修改要启动的模型
# ===========================================
MODEL_NAME="qwen3_0.6B"  # 修改此变量来切换模型
# 可选值: minimind2, qwen3_0.6B, [其他模型名]
# ===========================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查配置文件是否存在
check_config_files() {
    local base_config="docker-compose.yml"
    local model_config="models/docker-compose-${MODEL_NAME}.yml"
    
    if [ ! -f "$base_config" ]; then
        print_error "Base config file not found: $base_config"
        return 1
    fi
    
    if [ ! -f "$model_config" ]; then
        print_error "Model config file not found: $model_config"
        print_info "Available model configs:"
        ls models/docker-compose-*.yml 2>/dev/null | grep -v template | sed 's/models\/docker-compose-//g' | sed 's/\.yml//g' | sed 's/^/  - /'
        return 1
    fi
    
    return 0
}

# 停止现有的 vLLM 服务
stop_existing_service() {
    print_info "Checking for existing vLLM services..."
    
    # 查找所有运行中的 vLLM 容器
    local running_containers=$(docker ps --filter "name=vllm" --format "{{.Names}}" 2>/dev/null)
    
    if [ -n "$running_containers" ]; then
        print_warning "Found running vLLM containers, stopping them..."
        echo "$running_containers" | while read container; do
            print_info "Stopping container: $container"
            docker stop "$container" >/dev/null 2>&1
            docker rm "$container" >/dev/null 2>&1
        done
        
        # 等待容器完全停止
        sleep 2
        print_success "Existing services stopped"
    else
        print_info "No existing vLLM services found"
    fi
}

# 启动 vLLM 服务
start_vllm_service() {
    local base_config="docker-compose.yml"
    local model_config="models/docker-compose-${MODEL_NAME}.yml"
    
    print_info "Starting vLLM service with model: $MODEL_NAME"
    print_info "Base config: $base_config"
    print_info "Model config: $model_config"
    
    # 使用配置覆盖模式启动
    docker-compose -f "$base_config" -f "$model_config" up -d
    
    if [ $? -eq 0 ]; then
        print_success "vLLM service started successfully!"
        echo
        print_info "Service details:"
        echo "  Model: $MODEL_NAME"
        echo "  URL: http://localhost:18080"
        echo
        print_info "Useful commands:"
        echo "  View logs: docker-compose -f $base_config -f $model_config logs -f"
        echo "  Stop service: docker-compose -f $base_config -f $model_config down"
        echo "  Check status: docker ps --filter \"name=vllm\""
        echo
        print_info "Waiting for service to be ready..."
        
        # 等待服务启动
        local max_wait=60
        local wait_time=0
        while [ $wait_time -lt $max_wait ]; do
            if curl -s http://localhost:18080/health >/dev/null 2>&1; then
                print_success "Service is ready and healthy!"
                break
            fi
            echo -n "."
            sleep 2
            wait_time=$((wait_time + 2))
        done
        
        if [ $wait_time -ge $max_wait ]; then
            print_warning "Service may still be starting up. Check logs if needed."
        fi
        
    else
        print_error "Failed to start vLLM service!"
        return 1
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "         vLLM Model Launcher"
    echo "========================================"
    echo
    
    print_info "Target model: $MODEL_NAME"
    echo
    
    # 检查配置文件
    if ! check_config_files; then
        exit 1
    fi
    
    # 停止现有服务
    stop_existing_service
    
    # 启动新服务
    if start_vllm_service; then
        print_success "vLLM service is running!"
    else
        print_error "Failed to start vLLM service"
        exit 1
    fi
}

# 运行主函数
main "$@"
