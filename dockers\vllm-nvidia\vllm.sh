#!/bin/bash

# vLLM 模型管理脚本
# 使用配置覆盖模式管理不同的模型
# 支持命令: up, stop, down, list

# ===========================================
# 配置区域 - 在这里修改要启动的模型
# ===========================================
MODEL_NAME="qwen3_0.6B"  # 修改此变量来切换模型
# 可选值: minimind2, qwen3_0.6B, [其他模型名]
# ===========================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "========================================"
    echo "         vLLM 模型管理脚本"
    echo "========================================"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  up      启动 vLLM 服务 (默认)"
    echo "  stop    停止 vLLM 服务"
    echo "  down    停止并清理 vLLM 服务"
    echo "  list    列出可用的模型"
    echo "  help    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 up       # 启动服务"
    echo "  $0 stop     # 停止服务"
    echo "  $0 down     # 停止并清理服务"
    echo "  $0 list     # 列出可用模型"
    echo
    echo "当前配置的模型: $MODEL_NAME"
    echo "========================================"
}

# 列出可用的模型
do_list() {
    echo "========================================"
    echo "         可用的 vLLM 模型"
    echo "========================================"
    echo

    print_info "扫描 models/ 目录中的配置文件..."
    echo

    local model_configs=$(ls models/docker-compose-*.yml 2>/dev/null | grep -v template)

    if [ -z "$model_configs" ]; then
        print_warning "未找到任何模型配置文件"
        return 1
    fi

    echo "可用模型:"
    echo "$model_configs" | while read config_file; do
        local model_name=$(echo "$config_file" | sed 's/models\/docker-compose-//g' | sed 's/\.yml//g')
        if [ "$model_name" = "$MODEL_NAME" ]; then
            echo "  ✓ $model_name (当前配置)"
        else
            echo "  - $model_name"
        fi
    done

    echo
    print_info "要切换模型，请编辑脚本中的 MODEL_NAME 变量"
    echo "========================================"
}

# 检查配置文件是否存在
check_config_files() {
    local base_config="docker-compose.yml"
    local model_config="models/docker-compose-${MODEL_NAME}.yml"

    if [ ! -f "$base_config" ]; then
        print_error "Base config file not found: $base_config"
        return 1
    fi

    if [ ! -f "$model_config" ]; then
        print_error "Model config file not found: $model_config"
        print_info "Available model configs:"
        ls models/docker-compose-*.yml 2>/dev/null | grep -v template | sed 's/models\/docker-compose-//g' | sed 's/\.yml//g' | sed 's/^/  - /'
        return 1
    fi

    return 0
}

# 停止现有的 vLLM 服务
stop_existing_service() {
    print_info "Checking for existing vLLM services..."
    
    # 查找所有运行中的 vLLM 容器
    local running_containers=$(docker ps --filter "name=vllm" --format "{{.Names}}" 2>/dev/null)
    
    if [ -n "$running_containers" ]; then
        print_warning "Found running vLLM containers, stopping them..."
        echo "$running_containers" | while read container; do
            print_info "Stopping container: $container"
            docker stop "$container" >/dev/null 2>&1
            docker rm "$container" >/dev/null 2>&1
        done
        
        # 等待容器完全停止
        sleep 2
        print_success "Existing services stopped"
    else
        print_info "No existing vLLM services found"
    fi
}

# 启动 vLLM 服务
do_up() {
    echo "========================================"
    echo "         启动 vLLM 服务"
    echo "========================================"
    echo

    print_info "Target model: $MODEL_NAME"
    echo

    # 检查配置文件
    if ! check_config_files; then
        exit 1
    fi

    # 停止现有服务
    stop_existing_service

    # 启动新服务
    local base_config="docker-compose.yml"
    local model_config="models/docker-compose-${MODEL_NAME}.yml"

    print_info "Starting vLLM service with model: $MODEL_NAME"
    print_info "Base config: $base_config"
    print_info "Model config: $model_config"

    # 使用配置覆盖模式启动
    docker-compose -f "$base_config" -f "$model_config" up -d

    if [ $? -eq 0 ]; then
        print_success "vLLM service started successfully!"
        echo
        print_info "Service details:"
        echo "  Model: $MODEL_NAME"
        echo "  URL: http://localhost:18080"
        echo
        print_info "Useful commands:"
        echo "  View logs: docker-compose -f $base_config -f $model_config logs -f"
        echo "  Stop service: $0 stop"
        echo "  Stop and cleanup: $0 down"
        echo "  Check status: docker ps --filter \"name=vllm\""
        echo
        print_info "Waiting for service to be ready..."

        # 等待服务启动
        local max_wait=60
        local wait_time=0
        while [ $wait_time -lt $max_wait ]; do
            if curl -s http://localhost:18080/health >/dev/null 2>&1; then
                print_success "Service is ready and healthy!"
                break
            fi
            echo -n "."
            sleep 2
            wait_time=$((wait_time + 2))
        done

        if [ $wait_time -ge $max_wait ]; then
            print_warning "Service may still be starting up. Check logs if needed."
        fi

    else
        print_error "Failed to start vLLM service!"
        return 1
    fi
}

# 停止 vLLM 服务
do_stop() {
    echo "========================================"
    echo "         停止 vLLM 服务"
    echo "========================================"
    echo

    print_info "Target model: $MODEL_NAME"
    echo

    # 检查配置文件
    if ! check_config_files; then
        print_warning "配置文件不存在，尝试停止所有 vLLM 容器..."
        stop_existing_service
        return 0
    fi

    local base_config="docker-compose.yml"
    local model_config="models/docker-compose-${MODEL_NAME}.yml"

    print_info "Stopping vLLM service with model: $MODEL_NAME"

    # 使用配置覆盖模式停止
    docker-compose -f "$base_config" -f "$model_config" stop

    if [ $? -eq 0 ]; then
        print_success "vLLM service stopped successfully!"
        echo
        print_info "容器已停止但未删除，可以使用以下命令重新启动:"
        echo "  $0 up"
        echo
        print_info "要完全清理服务，请使用:"
        echo "  $0 down"
    else
        print_error "Failed to stop vLLM service!"
        return 1
    fi
}

# 停止并清理 vLLM 服务
do_down() {
    echo "========================================"
    echo "         停止并清理 vLLM 服务"
    echo "========================================"
    echo

    print_info "Target model: $MODEL_NAME"
    echo

    # 检查配置文件
    if ! check_config_files; then
        print_warning "配置文件不存在，尝试停止并删除所有 vLLM 容器..."
        stop_existing_service
        return 0
    fi

    local base_config="docker-compose.yml"
    local model_config="models/docker-compose-${MODEL_NAME}.yml"

    print_info "Stopping and removing vLLM service with model: $MODEL_NAME"

    # 使用配置覆盖模式停止并清理
    docker-compose -f "$base_config" -f "$model_config" down

    if [ $? -eq 0 ]; then
        print_success "vLLM service stopped and cleaned up successfully!"
        echo
        print_info "所有容器、网络和卷已被清理"
        echo
        print_info "要重新启动服务，请使用:"
        echo "  $0 up"
    else
        print_error "Failed to stop and cleanup vLLM service!"
        return 1
    fi
}

# 主函数
main() {
    local command="$1"

    # 如果没有参数，默认执行 up 命令
    if [ -z "$command" ]; then
        command="up"
    fi

    case "$command" in
        up)
            do_up
            ;;
        stop)
            do_stop
            ;;
        down)
            do_down
            ;;
        list)
            do_list
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "未知命令: $command"
            echo
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
