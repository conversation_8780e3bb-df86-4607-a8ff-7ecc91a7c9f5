---
description: 
globs: 
alwaysApply: false
---
# Design First Development Rule

## Core Principle
Always design the solution before writing code. Complete and validate the design before implementation begins.

## Rule Statement
**Design First**: No code shall be written until the solution design is thoroughly planned, reviewed, and approved.

## Implementation Guidelines

### 1. Design Phase Requirements
- **Follow Design Principles**: All designs must comply with the principles outlined in [arch-design.mdc](mdc:study-kits/.cursor/rules/arch-design.mdc)
- **Solution Architecture**: Define the overall system structure and component relationships
- **Data Models**: Specify data structures, schemas, and relationships
- **API Contracts**: Document interfaces, endpoints, and data formats
- **Logic Flow**: Create flowcharts or pseudocode for complex business logic
- **Error Handling**: Plan exception scenarios and recovery strategies

### 2. Design Validation Checklist
- [ ] Design principles from [arch-design.mdc](mdc:study-kits/.cursor/rules/arch-design.mdc) are followed
- [ ] Requirements are fully addressed
- [ ] Design is technically feasible
- [ ] Performance considerations are evaluated
- [ ] Security implications are assessed
- [ ] Scalability requirements are met
- [ ] Dependencies are identified and documented

### 3. Design Documentation Format
```
## Problem Statement
[Clear description of what needs to be solved]

## Proposed Solution
[High-level approach and methodology]

## Technical Specifications
[Detailed technical requirements and constraints]

## Implementation Plan
[Step-by-step breakdown of development tasks]
```

### 4. Coding Phase Rules
- **Only begin coding after design approval**
- **Follow the approved design strictly**
- **Adhere to architectural principles defined in [arch-design.mdc](mdc:study-kits/.cursor/rules/arch-design.mdc) **
- **Document any design deviations with justification**
- **Update design documentation when changes are necessary**

### 5. Information Gathering Exception
When the "Implementation Plan" includes actions to **check** or **retrieve** information:
- These operations can be executed immediately without prior confirmation
- No approval is needed for read-only operations
- This includes: file inspection, data querying, configuration checking, dependency analysis
- **Rationale**: Information gathering does not modify code and poses no risk to the system

## Benefits
- Reduces development time and debugging effort
- Improves code quality and maintainability
- Minimizes architectural debt and refactoring needs
- Ensures comprehensive requirement coverage
- Facilitates better code reviews and team collaboration
- Allows efficient information gathering without workflow interruption

## Violation Consequences
Coding without proper design leads to:
- Increased technical debt
- Higher maintenance costs
- Poor system performance
- Difficult debugging and troubleshooting
- Inconsistent code quality

---

*Remember: Time spent in design is time saved in development and maintenance.*