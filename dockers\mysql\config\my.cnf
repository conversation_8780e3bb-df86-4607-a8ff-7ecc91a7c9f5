[mysqld]
# 基础配置
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# 连接配置
max_connections = 200
max_connect_errors = 100
connect_timeout = 10
wait_timeout = 28800
interactive_timeout = 28800

# 内存配置 (针对2GB内存限制优化)
innodb_buffer_pool_size = 1024M
innodb_log_buffer_size = 16M
key_buffer_size = 128M
sort_buffer_size = 2M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
myisam_sort_buffer_size = 64M
thread_cache_size = 8

# InnoDB 配置
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_file_size = 256M
innodb_log_files_in_group = 2
innodb_flush_method = O_DIRECT
innodb_lock_wait_timeout = 50

# 日志配置 - 修改为容器内路径
log-error = /var/lib/mysql/mysql-error.log
slow_query_log = 1
slow_query_log_file = /var/lib/mysql/mysql-slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# 安全配置
local-infile = 0
# skip-show-database   # 隐藏数据库列表
# skip-symbolic-links  # 禁用符号链接

# 二进制日志配置 (用于备份和复制)
log-bin = /var/lib/mysql/mysql-bin
binlog_format = ROW
binlog_expire_logs_seconds = 604800  # 7天
max_binlog_size = 100M

# 性能优化
tmp_table_size = 64M
max_heap_table_size = 64M
table_open_cache = 2000
thread_stack = 192K

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock