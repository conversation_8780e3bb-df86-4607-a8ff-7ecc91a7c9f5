github 
    https://github.com/usual2970/certimate


下载
    https://github.com/usual2970/certimate/releases/tag/v0.3.17

浏览器中访问 http://127.0.0.1:8910。

初始的管理员账号及密码：

账号：<EMAIL>
密码：1234567890
即刻使用 Certimate。

# certimate
如果安装到 docker 里，会存在无法启动nginx服务的问题，所以必须以 linux 服务启动的方式运行;

# 查看状态
sudo systemctl status certimate.service

# 启用服务
sudo systemctl enable certimate.service

# 启动服务
sudo systemctl start certimate.service

# 停止服务
sudo systemctl stop certimate.service

# 修改配置
sudo vi /etc/systemd/system/certimate.service

# 重启服务
sudo systemctl restart certimate.service

# 重新加载配置
sudo systemctl daemon-reload