-- MySQL 初始化脚本
-- 配置 root 用户权限，允许从 Docker 网络连接

-- 确认用户是否存在，必要时创建用户
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '&o&Iyn*vO9(_';
-- 修改已存在用户的密码
ALTER USER 'root'@'%' IDENTIFIED BY '&o&Iyn*vO9(_';
-- 授予所有权限
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
-- 刷新权限
FLUSH PRIVILEGES;

-- 显示当前用户列表（用于调试）
SELECT user, host FROM mysql.user WHERE user='root';

-- 使用说明：
-- 1. 将需要初始化的 SQL 文件放在此目录下
-- 2. 文件将按字母顺序执行
-- 3. 建议使用数字前缀命名，如：01_create_database.sql, 02_create_tables.sql
--
-- 示例：
-- CREATE DATABASE IF NOT EXISTS your_database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
