# PostgreSQL Docker 配置

基于 PostgreSQL 17.5 的 Docker 容器配置，提供完整的数据库服务和管理工具。


## 快速开始


### 0. 准备工作

```bash
# 先在 /home/<USER>/ 创建一个 postgres/data 目录
mkdir -p /home/<USER>/postgres/data
mkdir -p /home/<USER>/postgres/backups

# 999是postgres容器内postgres用户的UID/GID
sudo chown -R 999:999 /home/<USER>/postgres 

## 999通常是MySQL官方Docker镜像中mysql用户的默认UID/GID，但这不是一个固定的值，可能会根据MySQL镜像版本有所变化。
## 产看方法:
docker run --rm postgres:8.4.5 bash -c "id postgres"
```

### 1. 启动服务

```bash
# 进入 postgres 目录
cd dockers/postgres

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 2. 导出数据

```bash
# 进入容器
docker-compose exec postgres bash

## 退出容器(容器继续运行)
组合键: Ctrl+P, Ctrl+Q

## 停止容器运行
exit

# 导出数据
pg_dump -U postgres -h localhost -d max > /var/backups/postgres/max.sql
```

## 配置说明

### 环境变量

主要环境变量在 `.env` 文件中配置：

- `POSTGRES_ROOT_PASSWORD`: 数据库密码
- `POSTGRES_USER`: 数据库用户（默认 postgres）
- `POSTGRES_DEFAULT_DB`: 默认数据库（默认 postgres）
- `POSTGRES_HOST_PORT`: 主机端口（默认 15432）
- `POSTGRES_CONTAINER_PORT`: 容器端口（默认 5432）
- `POSTGRES_MEMORY_LIMIT`: 内存限制（默认 2g）

### 性能优化

配置文件已针对 2GB 内存限制进行优化：

- `shared_buffers`: 512MB (25% of RAM)
- `effective_cache_size`: 1536MB (75% of RAM)
- `work_mem`: 4MB
- `maintenance_work_mem`: 128MB

### 安全配置

- 使用 `scram-sha-256` 密码加密
- 启用连接日志
- 禁用新权限提升
- 使用 postgres 用户运行

## 备份和恢复

### 自动备份

备份服务会根据 `BACKUP_SCHEDULE` 环境变量自动执行备份：

- 默认每天凌晨 2 点执行备份
- 自动清理 7 天前的备份文件
- 备份包括所有用户数据库和全局对象


## 监控和维护

### 健康检查

容器配置了健康检查，使用 `pg_isready` 命令检查服务状态。

### 日志管理

- 错误日志：记录警告级别以上的消息
- 慢查询日志：记录执行时间超过 1 秒的查询
- 连接日志：记录连接和断开事件

### 性能监控

启用了 `pg_stat_statements` 扩展，可以监控查询性能：

```sql
-- 查看最耗时的查询
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;
```

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查端口是否被占用
   - 检查数据目录权限
   - 查看容器日志

2. **连接被拒绝**
   - 确认服务已启动
   - 检查防火墙设置
   - 验证用户名和密码

3. **性能问题**
   - 检查内存使用情况
   - 分析慢查询日志
   - 调整配置参数

### 查看日志

```bash
# 查看 PostgreSQL 日志
docker-compose logs postgres

# 查看备份服务日志
docker-compose logs postgres-backup

# 实时查看日志
docker-compose logs -f
```
