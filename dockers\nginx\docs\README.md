# Nginx Docker 多服务部署指南

## 📋 项目概述

基于Docker的Nginx服务，支持很多www服务的统一管理和部署。

### 端口配置
- `80/443` → 主网站 (HTTP/HTTPS)

## 🚀 本地测试部署

### 1. 准备SSL证书
```bash
cd dockers/nginx/certs

# 生成证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout server.key -out server.crt -config server.conf -extensions v3_req

# 查看证书中是否包含域名和IP地址:
openssl x509 -in server.crt -text -noout
# 验证证书内容
openssl x509 -in server.crt -noout -subject -issuer
# 验证SAN扩展
openssl x509 -in server.crt -noout -ext subjectAltName

```

### 2. 创建网络
```bash
docker network create app_network
```

### 3. 启动服务
```bash
cd dockers/nginx
docker-compose up -d
```

### 4. 验证服务
```bash
docker-compose ps
docker logs nginx_service
```

## 🚀 服务器部署

### 1. 上传配置文件【重点】
将 `dockers/nginx/` 目录下的所有文件复制到服务器的 `/home/<USER>/dockers/nginx/` 目录

### 2. 服务器端部署【重点】
```bash
cd /home/<USER>/dockers/nginx

## 安全重启（推荐）
docker-compose down && docker-compose up -d
## 当配置变更时, 强制重建并启动【重点】
docker-compose down && docker-compose up -d --force-recreate


```

## 🔧 添加新Demo服务

### 1. 添加网站文件
```bash
# 创建新demo目录
mkdir www/demo2
echo "<h1>Demo2 Service</h1>" > www/demo2/index.html
```

### 2. 修改nginx.conf
添加新的server块，参考现有demo1配置。

### 3. 修改docker-compose.yml
在nginx的ports部分添加新端口映射：
```yaml
- "10315:10315"  # 新demo端口
```

### 4. 重新部署
```bash
docker-compose down && docker-compose up -d
```

## 📝 服务管理

### 本地操作
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart nginx

# 查看日志
docker logs nginx_service --tail 20

# 重新加载nginx配置
docker exec nginx_service nginx -s reload
```

### 服务器操作
**上传并部署**：将 `dockers/nginx/` 目录下的所有文件复制到服务器的 `/home/<USER>/dockers/nginx/` 目录，然后执行部署命令

**查看服务器状态**：
```bash
docker ps 
docker logs nginx_service --tail 10
```

## 🛠️ 常用诊断命令

```bash
# 检查容器状态
docker ps

# 测试端口连通性
curl -k https://localhost:8312

# 检查nginx配置语法
docker exec nginx_service nginx -t

# 查看网络
docker network ls
```

## ⚠️ 注意事项

- 端口范围：103xx用于业务服务
- 所有服务必须加入app_network网络
- SSL证书需要包含所有访问域名
- 新增demo服务只需添加www目录和nginx配置

---

**版本**: v2.0 - 支持很多www服务的统一架构