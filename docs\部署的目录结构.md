# 部署到 ubuntu 后的目录结构

## 目录结构
账户名: ubuntu
基础目录: /home/<USER>/
```bash
.
├── dockers
│   ├── mysql (耗内存507M)
│   │   ├── config
│   │   ├── init
│   │   └── docker-compose.yml
│   ├── nginx (耗内存23M)
│   │   ├── certs
│   │   ├── config
│   │   └── docker-compose.yml
│   ├── postgres (耗内存150M)
│   │   ├── config
│   │   ├── init
│   │   └── docker-compose.yml
│   └── redis (耗内存14M)
│       ├── config
│       └── docker-compose.yml
├── volumes
│   ├── mysql
│   │   ├── backup
│   │   └── data
│   ├── postgres
│   │   ├── backup
│   │   └── data
│   └── redis
│       └── data
└── www
    └── test
        └── index.html
```

## 两个基础命令

```bash
# 权限命令
sudo chown -R 999:999 /home/<USER>/volumes 

# 创建network
docker network create app_network
```

