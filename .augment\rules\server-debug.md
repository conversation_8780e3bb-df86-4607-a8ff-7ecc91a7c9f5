---
alwaysApply: true
---

# DEBUG-SERVER-Info

## 操作服务器基本要求

- **谨慎原则**: 修改内容务必谨慎;
- **最小原则**: 服务器资源很小，应用的方案要先考虑节省内存和磁盘资源;
- **干净原则**: 不要保留修改后不需要的内容，包括尝试失败的安装、修改;

## 服务器信息

- **公网IP**: *************
- **内网IP**: *********
- **服务器配置**: 2核4G
- **操作系统**: Ubuntu Server 22.04 LTS 64bit
- **Docker版本**: 28.2.2
- **Docker-Compose版本**: 1.29.2

## 连接服务器账户和方式

```bash
ssh ubuntu@*************
```

## docker-compose工作目录

```bash
# 根目录
/home/<USER>/dockers

# nginx 目录
/home/<USER>/dockers/nginx

# nginx 用到的SSL证书目录
/home/<USER>/dockers/nginx/certs/pro

# redis 目录
/home/<USER>/dockers/redis

# postgres 目录
/home/<USER>/dockers/postgres

```

## docker容器挂载的volumes目录

```bash
## volumes 根目录
/home/<USER>/volumes/

# redis 目录
/home/<USER>/volumes/redis

# postgres 目录
/home/<USER>/volumes/postgres
```

