# Linux编译专用Dockerfile
FROM ubuntu:22.04

# 设置非交互式安装
ENV DEBIAN_FRONTEND=noninteractive

# 更换apt源并安装开发工具
RUN sed -i "s/archive.ubuntu.com/mirrors.tuna.tsinghua.edu.cn/g" /etc/apt/sources.list && \
    sed -i "s/security.ubuntu.com/mirrors.tuna.tsinghua.edu.cn/g" /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y \
        wget \
        git \
        gcc \
        build-essential \
        ca-certificates \
        curl \
        vim \
        htop && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 安装Go
ARG GO_VERSION=1.24.2
RUN wget -q https://mirrors.aliyun.com/golang/go${GO_VERSION}.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz && \
    rm go${GO_VERSION}.linux-amd64.tar.gz

# 设置Go环境
ENV PATH=$PATH:/usr/local/go/bin
ENV GOPATH=/go
ENV GOCACHE=/go/cache
ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=1

# 创建工作目录
WORKDIR /app

# 创建日志目录
RUN mkdir -p /root/logs && chmod -R 755 /root/logs

# 暴露端口
EXPOSE 9070

# 默认命令 - 可以被docker-compose覆盖
CMD ["tail", "-f", "/dev/null"]
