# ============================================================================
# Linux Builder - Go 编译环境容器
# ============================================================================
#
# 功能说明:
#   - 提供干净的 Ubuntu Linux 编译环境
#   - 预装 Go 编译工具链和开发工具
#   - 用于将 macOS/Windows 开发的代码编译成 Linux 二进制文件
#   - 支持 Go 模块缓存，避免重复下载依赖
#
# 使用方式:
#   1. 启动容器: docker-compose up -d
#   2. 进入容器: docker-compose exec linux-builder bash
#   3. 编译代码: cd /app/services/go-apps/your-project && make build
#   4. 打包发布: make release
#
# 注意事项:
#   - 此容器仅用于编译，不提供网络服务，因此无端口映射
#   - 容器持续运行以便随时进入执行编译任务
#   - 通过卷挂载共享本地代码和缓存目录
# ============================================================================

services:
  linux-builder:
    image: linux-builder:latest
    container_name: hv-apps-linux-builder
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      # 映射整个services目录，保持目录结构
      - ../:/app/services
      # 映射Go模块缓存，避免重复下载
      - ./go-caches/go-cache:/go
      # 映射Go构建缓存
      - ./go-caches/go-build-cache:/root/.cache/go-build
    restart: always
    working_dir: /app/services/go-apps/
    # 使用 tail 命令保持容器运行，方便随时进入执行编译任务
    command: ["tail", "-f", "/dev/null"]
    environment:
      # Go 模块支持
      - GO111MODULE=on
      # 使用国内代理加速模块下载
      - GOPROXY=https://goproxy.cn,direct
      # 启用 CGO 支持
      - CGO_ENABLED=1
