# MySQL Docker 快速参考

## 常用命令速查

### 🚀 服务管理
```bash
# 启动服务
docker-compose up -d
## 强制启动
docker-compose up -d --force-recreate

# 停止服务
docker-compose down

# 删除数据卷
docker volume rm mysql_data mysql_logs mysql_backups

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs mysql
```

### 📊 进入容器
```bash
# 进入MySQL容器
docker-compose exec postgres bash
docker-compose exec postgres /bin/bash

# 退出容器命令行(容器仍在运行)
敲2个组合键：Ctrl+P , Ctrl+Q  

# 退出并停止容器(容器停止)
exit

```

### 📊 数据库管理
```bash
# 列出所有数据库
docker-compose exec mysql ./scripts/manage_db.sh list

# 创建数据库（仅root可访问）
docker-compose exec mysql ./scripts/manage_db.sh create myapp_db

# 创建数据库+专用用户（推荐）
docker-compose exec mysql ./scripts/manage_db.sh create-with-user myapp_db myapp_user 'MyStr0ng@Pass2025'

# 删除数据库
docker-compose exec mysql ./scripts/manage_db.sh drop myapp_db

# 查看数据库信息
docker-compose exec mysql ./backup/scripts/manage_db.sh show myapp_db
docker-compose exec mysql ./backup/scripts/manage_db.sh tables myapp_db
docker-compose exec mysql ./backup/scripts/manage_db.sh size myapp_db
```

### 👥 用户管理
```bash
# 列出所有用户
docker-compose exec mysql ./backup/scripts/manage_db.sh list-users

# 创建用户
docker-compose exec mysql ./backup/scripts/manage_db.sh create-user username 'Strong@Pass2025'

# 删除用户
docker-compose exec mysql ./backup/scripts/manage_db.sh drop-user username

# 查看用户权限
docker-compose exec mysql ./backup/scripts/manage_db.sh show-grants username

# 授权用户访问数据库
docker-compose exec mysql /backup/scripts/manage_db.sh grant database_name username

# 撤销用户数据库权限
docker-compose exec mysql ./scripts/manage_db.sh revoke database_name username
```

### 💾 备份与恢复
```bash
# 用 postgres 的工具 pg_dump 备份;

# 模板命令:
pg_dump -s databasename > dump.sql

# 本例:
pg_dump -s max > /backup/max.sql
## max.sql 存放到了/home/<USER>/postgres/backup目录下

```

```bash
# 手动备份所有数据库
docker-compose exec mysql-backup /scripts/backup.sh

# 列出备份文件
docker-compose exec mysql /scripts/manage_db.sh backup-list

# 恢复数据库
docker-compose exec mysql /scripts/restore.sh database_name backup_file.sql.gz
```

### 🔗 数据库连接
```bash
# 使用root连接
docker-compose exec mysql mysql -u root -p

# 使用指定用户连接（容器内部）
docker-compose exec mysql mysql -u username -p database_name

# 外部连接（从宿主机）
mysql -h localhost -P 15306 -u username -p database_name
```

## 📋 常用场景模板

### 新项目数据库设置
```bash
# 1. 创建项目数据库和用户
docker-compose exec mysql /scripts/manage_db.sh create-with-user project_db project_user 'Pr0ject#2025!DB'

# 2. 验证设置
docker-compose exec mysql /scripts/manage_db.sh show-grants project_user

# 3. 应用程序连接信息
# 主机: localhost:15306 (或 127.0.0.1:15306)
# 数据库: project_db
# 用户: project_user
# 密码: Pr0ject#2025!DB
```

### 多环境数据库
```bash
# 开发环境
docker-compose exec mysql /scripts/manage_db.sh create-with-user app_dev dev_user 'Dev#2025!Pass'

# 测试环境
docker-compose exec mysql /scripts/manage_db.sh create-with-user app_test test_user 'Test#2025!Pass'

# 生产环境
docker-compose exec mysql /scripts/manage_db.sh create-with-user app_prod prod_user 'Pr0d#2025!Pass'
```

### 只读用户创建
```bash
# 1. 创建只读用户
docker-compose exec mysql /scripts/manage_db.sh create-user readonly_user 'Read#2025!Only'

# 2. 授予只读权限
docker-compose exec mysql mysql -u root -p -e "GRANT SELECT ON myapp_db.* TO 'readonly_user'@'%'; FLUSH PRIVILEGES;"
```

## ⚠️ 重要提醒

- **密码安全**: 使用包含大小写字母、数字、特殊字符的强密码
- **权限最小化**: 每个用户只授予必要的权限
- **定期备份**: 自动备份每天凌晨2点执行，保留7天
- **环境隔离**: 不同环境使用不同的用户和密码
- **权限审查**: 定期检查和清理不需要的用户权限

## 🆘 故障排除

```bash
# 检查服务状态
docker-compose ps

# 查看详细日志
docker-compose logs mysql

# 测试数据库连接（容器内部）
docker-compose exec mysql mysql -u root -p -e "SELECT 1;"

# 测试外部连接（从宿主机）
mysql -h localhost -P 15306 -u root -p -e "SELECT 1;"

# 检查用户权限
docker-compose exec mysql /backup/scripts/manage_db.sh show-grants username

# 重置用户密码
docker-compose exec mysql mysql -u root -p -e "ALTER USER 'username'@'%' IDENTIFIED BY 'new_password';"
```
